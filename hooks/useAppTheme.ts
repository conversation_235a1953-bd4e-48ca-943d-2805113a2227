import { useColorScheme as useRNColorScheme } from 'react-native';
import { useEffect, useState } from 'react';
import { getSetting, setSetting } from '@/lib/settings';

export type ThemeMode = 'system' | 'light' | 'dark';

export function useAppTheme() {
  const systemScheme = useRNColorScheme();
  const [mode, setMode] = useState<ThemeMode>('system');
  const [effective, setEffective] = useState<'light' | 'dark'>(systemScheme ?? 'light');

  useEffect(() => {
    (async () => {
      const stored = await getSetting('themeMode');
      setMode(stored);
    })();
  }, []);

  useEffect(() => {
    const eff = mode === 'system' ? (systemScheme ?? 'light') : mode;
    setEffective(eff as 'light' | 'dark');
  }, [mode, systemScheme]);

  const setThemeMode = async (m: ThemeMode) => {
    setMode(m);
    await setSetting('themeMode', m);
  };

  return { mode, effective, setThemeMode };
}