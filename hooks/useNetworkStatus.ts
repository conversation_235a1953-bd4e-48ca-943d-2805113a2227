import { useState, useEffect } from 'react'
import { Platform } from 'react-native'

interface NetworkState {
  isConnected: boolean
  isInternetReachable: boolean | null
  type: string | null
}

export function useNetworkStatus() {
  const [networkState, setNetworkState] = useState<NetworkState>({
    isConnected: true,
    isInternetReachable: null,
    type: null,
  })

  useEffect(() => {
    // For web, we can use the navigator.onLine API
    if (Platform.OS === 'web') {
      const updateOnlineStatus = () => {
        setNetworkState({
          isConnected: navigator.onLine,
          isInternetReachable: navigator.onLine,
          type: 'unknown',
        })
      }

      updateOnlineStatus()

      window.addEventListener('online', updateOnlineStatus)
      window.addEventListener('offline', updateOnlineStatus)

      return () => {
        window.removeEventListener('online', updateOnlineStatus)
        window.removeEventListener('offline', updateOnlineStatus)
      }
    } else {
      // For React Native, we would use @react-native-community/netinfo
      // But for now, we'll assume connected on mobile platforms
      setNetworkState({
        isConnected: true,
        isInternetReachable: true,
        type: 'unknown',
      })
    }
  }, [])

  return {
    isConnected: networkState.isConnected,
    isInternetReachable: networkState.isInternetReachable,
    type: networkState.type,
    isOffline: !networkState.isConnected,
  }
}