import React, { useState } from 'react'
import {
  View,
  Text,
  TextInput,
  Pressable,
  Alert,
  ActivityIndicator,
  ScrollView,
} from 'react-native'
import { useTranslation } from 'react-i18next'
import { useAuth } from '@/contexts/AuthContext'

interface LoginScreenProps {
  onSwitchToSignUp: () => void
  onForgotPassword: () => void
}

export function LoginScreen({ onSwitchToSignUp, onForgotPassword }: LoginScreenProps) {
  const { t } = useTranslation()
  const { signIn, loading } = useAuth()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSignIn = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert(t('auth.error'), t('auth.fillAllFields'))
      return
    }

    setIsSubmitting(true)
    try {
      await signIn(email.trim(), password)
      // 登录成功后会自动跳转
    } catch (error: any) {
      Alert.alert(t('auth.error'), error.message || t('auth.loginFailed'))
    } finally {
      setIsSubmitting(false)
    }
  }

  const isLoading = loading || isSubmitting

  return (
    <ScrollView className="flex-1 bg-gray-50">
      <View className="flex-1 justify-center px-6 py-12">
        <View className="bg-white rounded-2xl p-6 shadow-sm">
          <Text className="text-3xl font-bold text-center mb-2">
            {t('auth.welcomeBack')}
          </Text>
          <Text className="text-gray-600 text-center mb-8">
            {t('auth.signInToAccount')}
          </Text>

          <View className="mb-4">
            <Text className="text-gray-700 mb-2 font-medium">
              {t('auth.email')}
            </Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-4 py-3 text-base"
              placeholder={t('auth.emailPlaceholder')}
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              editable={!isLoading}
            />
          </View>

          <View className="mb-6">
            <Text className="text-gray-700 mb-2 font-medium">
              {t('auth.password')}
            </Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-4 py-3 text-base"
              placeholder={t('auth.passwordPlaceholder')}
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              editable={!isLoading}
            />
          </View>

          <Pressable
            onPress={onForgotPassword}
            className="mb-6"
            disabled={isLoading}
          >
            <Text className="text-blue-600 text-right font-medium">
              {t('auth.forgotPassword')}
            </Text>
          </Pressable>

          <Pressable
            onPress={handleSignIn}
            className={`bg-blue-600 rounded-lg py-4 mb-6 ${
              isLoading ? 'opacity-50' : ''
            }`}
            disabled={isLoading}
          >
            <View className="flex-row justify-center items-center">
              {isSubmitting && (
                <ActivityIndicator size="small" color="white" className="mr-2" />
              )}
              <Text className="text-white text-center font-semibold text-base">
                {t('auth.signIn')}
              </Text>
            </View>
          </Pressable>

          <View className="flex-row justify-center">
            <Text className="text-gray-600">
              {t('auth.noAccount')}{' '}
            </Text>
            <Pressable onPress={onSwitchToSignUp} disabled={isLoading}>
              <Text className="text-blue-600 font-medium">
                {t('auth.signUp')}
              </Text>
            </Pressable>
          </View>
        </View>
      </View>
    </ScrollView>
  )
}