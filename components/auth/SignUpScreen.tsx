import React, { useState } from 'react'
import {
  View,
  Text,
  TextInput,
  Pressable,
  Alert,
  ActivityIndicator,
  ScrollView,
} from 'react-native'
import { useTranslation } from 'react-i18next'
import { useAuth } from '@/contexts/AuthContext'

interface SignUpScreenProps {
  onSwitchToSignIn: () => void
}

export function SignUpScreen({ onSwitchToSignIn }: SignUpScreenProps) {
  const { t } = useTranslation()
  const { signUp, loading } = useAuth()
  const [name, setName] = useState('')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSignUp = async () => {
    if (!name.trim() || !email.trim() || !password.trim() || !confirmPassword.trim()) {
      Alert.alert(t('auth.error'), t('auth.fillAllFields'))
      return
    }

    if (password !== confirmPassword) {
      Alert.alert(t('auth.error'), t('auth.passwordMismatch'))
      return
    }

    if (password.length < 6) {
      Alert.alert(t('auth.error'), t('auth.passwordTooShort'))
      return
    }

    setIsSubmitting(true)
    try {
      await signUp(email.trim(), password, name.trim())
      Alert.alert(
        t('auth.success'),
        t('auth.signUpSuccess'),
        [{ text: t('common.ok'), onPress: onSwitchToSignIn }]
      )
    } catch (error: any) {
      Alert.alert(t('auth.error'), error.message || t('auth.signUpFailed'))
    } finally {
      setIsSubmitting(false)
    }
  }

  const isLoading = loading || isSubmitting

  return (
    <ScrollView className="flex-1 bg-gray-50">
      <View className="flex-1 justify-center px-6 py-12">
        <View className="bg-white rounded-2xl p-6 shadow-sm">
          <Text className="text-3xl font-bold text-center mb-2">
            {t('auth.createAccount')}
          </Text>
          <Text className="text-gray-600 text-center mb-8">
            {t('auth.signUpToStart')}
          </Text>

          <View className="mb-4">
            <Text className="text-gray-700 mb-2 font-medium">
              {t('auth.name')}
            </Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-4 py-3 text-base"
              placeholder={t('auth.namePlaceholder')}
              value={name}
              onChangeText={setName}
              autoCapitalize="words"
              editable={!isLoading}
            />
          </View>

          <View className="mb-4">
            <Text className="text-gray-700 mb-2 font-medium">
              {t('auth.email')}
            </Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-4 py-3 text-base"
              placeholder={t('auth.emailPlaceholder')}
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              editable={!isLoading}
            />
          </View>

          <View className="mb-4">
            <Text className="text-gray-700 mb-2 font-medium">
              {t('auth.password')}
            </Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-4 py-3 text-base"
              placeholder={t('auth.passwordPlaceholder')}
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              editable={!isLoading}
            />
          </View>

          <View className="mb-6">
            <Text className="text-gray-700 mb-2 font-medium">
              {t('auth.confirmPassword')}
            </Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-4 py-3 text-base"
              placeholder={t('auth.confirmPasswordPlaceholder')}
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry
              editable={!isLoading}
            />
          </View>

          <Pressable
            onPress={handleSignUp}
            className={`bg-blue-600 rounded-lg py-4 mb-6 ${
              isLoading ? 'opacity-50' : ''
            }`}
            disabled={isLoading}
          >
            <View className="flex-row justify-center items-center">
              {isSubmitting && (
                <ActivityIndicator size="small" color="white" className="mr-2" />
              )}
              <Text className="text-white text-center font-semibold text-base">
                {t('auth.signUp')}
              </Text>
            </View>
          </Pressable>

          <View className="flex-row justify-center">
            <Text className="text-gray-600">
              {t('auth.hasAccount')}{' '}
            </Text>
            <Pressable onPress={onSwitchToSignIn} disabled={isLoading}>
              <Text className="text-blue-600 font-medium">
                {t('auth.signIn')}
              </Text>
            </Pressable>
          </View>
        </View>
      </View>
    </ScrollView>
  )
}