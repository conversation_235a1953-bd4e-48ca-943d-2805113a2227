import React, { useState } from 'react'
import { Alert } from 'react-native'
import { useTranslation } from 'react-i18next'
import { LoginScreen } from './LoginScreen'
import { SignUpScreen } from './SignUpScreen'
import { useAuth } from '@/contexts/AuthContext'

type AuthMode = 'login' | 'signup'

export function AuthScreen() {
  const { t } = useTranslation()
  const { resetPassword } = useAuth()
  const [mode, setMode] = useState<AuthMode>('login')

  const handleForgotPassword = () => {
    Alert.prompt(
      t('auth.forgotPassword'),
      t('auth.enterEmailForReset'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('auth.sendResetEmail'),
          onPress: async (email) => {
            if (!email?.trim()) {
              Alert.alert(t('auth.error'), t('auth.emailRequired'))
              return
            }
            
            try {
              await resetPassword(email.trim())
              Alert.alert(
                t('auth.success'),
                t('auth.resetEmailSent')
              )
            } catch (error: any) {
              Alert.alert(
                t('auth.error'),
                error.message || t('auth.resetFailed')
              )
            }
          },
        },
      ],
      'plain-text',
      '',
      'email-address'
    )
  }

  if (mode === 'signup') {
    return (
      <SignUpScreen
        onSwitchToSignIn={() => setMode('login')}
      />
    )
  }

  return (
    <LoginScreen
      onSwitchToSignUp={() => setMode('signup')}
      onForgotPassword={handleForgotPassword}
    />
  )
}