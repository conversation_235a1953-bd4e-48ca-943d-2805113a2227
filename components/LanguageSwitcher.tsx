import { dp } from '@/lib/utils';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { StyleSheet, TouchableOpacity } from 'react-native';

import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';

export function LanguageSwitcher() {
  const { i18n, t } = useTranslation();

  const toggleLanguage = () => {
    const newLanguage = i18n.language === 'zh' ? 'en' : 'zh';
    i18n.changeLanguage(newLanguage);
  };

  const getCurrentLanguageLabel = () => {
    return i18n.language === 'zh' ? t('settings.languageOptions.zh') : t('settings.languageOptions.en');
  };

  return (
    <TouchableOpacity onPress={toggleLanguage} style={styles.container}>
      <ThemedView style={styles.button}>
        <ThemedText style={styles.text}>🌐 {getCurrentLanguageLabel()}</ThemedText>
      </ThemedView>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: dp(10),
  },
  button: {
    paddingHorizontal: dp(12),
    paddingVertical: dp(6),
    borderRadius: dp(16),
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  text: {
    fontSize: dp(14),
    fontWeight: '500',
  },
});