import { useThemeColor } from '@/hooks/useThemeColor';
import { dp } from '@/lib/utils';
import { StyleSheet, Text, type TextProps } from 'react-native';

export type ThemedTextProps = TextProps & {
  lightColor?: string;
  darkColor?: string;
  type?: 'default' | 'title' | 'defaultSemiBold' | 'subtitle' | 'link';
};

export function ThemedText({
  style,
  lightColor,
  darkColor,
  type = 'default',
  ...rest
}: ThemedTextProps) {
  const color = useThemeColor({ light: lightColor, dark: darkColor }, 'text');

  return (
    <Text
      style={[
        { color },
        type === 'default' ? styles.default : undefined,
        type === 'title' ? styles.title : undefined,
        type === 'defaultSemiBold' ? styles.defaultSemiBold : undefined,
        type === 'subtitle' ? styles.subtitle : undefined,
        type === 'link' ? styles.link : undefined,
        style,
      ]}
      {...rest}
    />
  );
}

const styles = StyleSheet.create({
  default: {
    fontSize: dp(16),
    lineHeight: dp(24),
  },
  defaultSemiBold: {
    fontSize: dp(16),
    lineHeight: dp(24),
    fontWeight: '600',
  },
  title: {
    fontSize: dp(32),
    fontWeight: 'bold',
    lineHeight: dp(32),
  },
  subtitle: {
    fontSize: dp(20),
    fontWeight: 'bold',
  },
  link: {
    lineHeight: dp(30), 
    fontSize: dp(16),
    color: '#0a7ea4',
  },
});
