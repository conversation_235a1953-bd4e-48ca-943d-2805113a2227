import React from 'react'
import { Dimensions, Pressable, Text, View } from 'react-native'
import Svg, { Line, Rect, Text as SvgText } from 'react-native-svg'

interface CandlestickData {
  date: string
  open: number
  high: number
  low: number
  close: number
}

interface CandlestickChartProps {
  data: CandlestickData[]
  width?: number
  height?: number
  onDataPointPress?: (data: CandlestickData, index: number) => void
}

export default function CandlestickChart({
  data,
  width = Dimensions.get('window').width - 64,
  height = 220,
  onDataPointPress,
}: CandlestickChartProps) {
  if (!data || data.length === 0) {
    return (
      <View style={{ width, height, justifyContent: 'center', alignItems: 'center' }}>
        <Text>No data available</Text>
      </View>
    )
  }

  const padding = 40
  const chartWidth = width - padding * 2
  const chartHeight = height - padding * 2

  // 计算价格范围
  const allPrices = data.flatMap((d) => [d.high, d.low])
  const minPrice = Math.min(...allPrices)
  const maxPrice = Math.max(...allPrices)
  const priceRange = maxPrice - minPrice

  // 计算Y轴比例
  const priceToY = (price: number) => {
    return chartHeight - ((price - minPrice) / priceRange) * chartHeight
  }

  // 计算X轴比例
  const indexToX = (index: number) => {
    return (index / (data.length - 1)) * chartWidth
  }

  // 绘制K线
  const renderCandlesticks = () => {
    return data.map((item, index) => {
      const x = indexToX(index)
      const openY = priceToY(item.open)
      const closeY = priceToY(item.close)
      const highY = priceToY(item.high)
      const lowY = priceToY(item.low)

      const isGreen = item.close >= item.open
      const color = isGreen ? '#4CD964' : '#FF3B30'
      const bodyHeight = Math.abs(closeY - openY)
      const bodyY = Math.min(openY, closeY)

      return (
        <View key={index}>
          {/* 影线（最高价到最低价） */}
          <Line x1={x} y1={highY} x2={x} y2={lowY} stroke={color} strokeWidth={1} />

          {/* 实体（开盘价到收盘价） */}
          <Rect
            x={x - 3}
            y={bodyY}
            width={6}
            height={Math.max(bodyHeight, 1)}
            fill={color}
            stroke={color}
            strokeWidth={1}
          />

          {/* 可点击区域 */}
          <Pressable
            style={{
              position: 'absolute',
              left: x - 15,
              top: Math.min(highY, lowY) - 10,
              width: 30,
              height: Math.abs(highY - lowY) + 20,
            }}
            onPress={() => onDataPointPress?.(item, index)}
          >
            <View style={{ width: '100%', height: '100%' }} />
          </Pressable>
        </View>
      )
    })
  }

  // 绘制网格线
  const renderGridLines = () => {
    const gridLines = []
    const numGridLines = 5

    for (let i = 0; i <= numGridLines; i++) {
      const y = (i / numGridLines) * chartHeight
      const price = maxPrice - (i / numGridLines) * priceRange

      gridLines.push(<Line key={`grid-${i}`} x1={0} y1={y} x2={chartWidth} y2={y} stroke="#E5E7EB" strokeWidth={0.5} />)

      // 价格标签
      gridLines.push(
        <SvgText key={`label-${i}`} x={chartWidth + 5} y={y + 4} fontSize={10} fill="#6B7280">
          {price.toFixed(4)}
        </SvgText>
      )
    }

    return gridLines
  }

  // 绘制X轴标签
  const renderXAxisLabels = () => {
    const labels = []
    const numLabels = Math.min(5, data.length)

    for (let i = 0; i < numLabels; i++) {
      const index = Math.floor((i / (numLabels - 1)) * (data.length - 1))
      const x = indexToX(index)
      const date = new Date(data[index].date)
      const label = date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      })

      labels.push(
        <SvgText key={`x-label-${i}`} x={x} y={chartHeight + 20} fontSize={10} fill="#6B7280" textAnchor="middle">
          {label}
        </SvgText>
      )
    }

    return labels
  }

  // 绘制趋势线
  const renderTrendLine = () => {
    if (data.length < 2) return null

    const points = data
      .map((item, index) => {
        const x = indexToX(index)
        const y = priceToY(item.close)
        return `${x},${y}`
      })
      .join(' ')

    return (
      <Line
        x1={indexToX(0)}
        y1={priceToY(data[0].close)}
        x2={indexToX(data.length - 1)}
        y2={priceToY(data[data.length - 1].close)}
        stroke="#007AFF"
        strokeWidth={1}
        strokeDasharray="5,5"
        opacity={0.6}
      />
    )
  }

  return (
    <View style={{ width, height }}>
      <Svg width={width} height={height}>
        {/* 背景 */}
        <Rect x={0} y={0} width={width} height={height} fill="#FFFFFF" />

        {/* 网格线和价格标签 */}
        {renderGridLines()}

        {/* 趋势线 */}
        {renderTrendLine()}

        {/* K线图 */}
        {renderCandlesticks()}

        {/* X轴标签 */}
        {renderXAxisLabels()}
      </Svg>
    </View>
  )
}
