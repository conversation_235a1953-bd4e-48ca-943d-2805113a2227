<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4299e1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#60a5fa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="coinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="arrowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e5e7eb;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 圆角背景 -->
  <rect width="512" height="512" rx="80" ry="80" fill="url(#bgGradient)"/>
  
  <!-- 阴影效果 -->
  <ellipse cx="256" cy="480" rx="200" ry="20" fill="#000000" opacity="0.1"/>
  
  <!-- 左侧货币符号 (美元) -->
  <circle cx="180" cy="200" r="60" fill="url(#coinGradient)" stroke="#ffffff" stroke-width="4"/>
  <text x="180" y="215" font-family="Arial, sans-serif" font-size="48" font-weight="bold" text-anchor="middle" fill="#ffffff">$</text>
  
  <!-- 右侧货币符号 (人民币) -->
  <circle cx="332" cy="312" r="60" fill="url(#coinGradient)" stroke="#ffffff" stroke-width="4"/>
  <text x="332" y="327" font-family="Arial, sans-serif" font-size="36" font-weight="bold" text-anchor="middle" fill="#ffffff">¥</text>
  
  <!-- 转换箭头 -->
  <g transform="translate(256, 256)">
    <!-- 双向箭头 -->
    <path d="M -40 -20 L 20 -20 L 10 -30 M 20 -20 L 10 -10" stroke="url(#arrowGradient)" stroke-width="6" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M 40 20 L -20 20 L -10 30 M -20 20 L -10 10" stroke="url(#arrowGradient)" stroke-width="6" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
  </g>
  
  <!-- 图表元素 -->
  <g transform="translate(100, 380)">
    <!-- 简化的折线图 -->
    <path d="M 0 40 L 60 20 L 120 35 L 180 15 L 240 25 L 300 10" stroke="#ffffff" stroke-width="4" fill="none" stroke-linecap="round" opacity="0.8"/>
    <!-- 数据点 -->
    <circle cx="0" cy="40" r="4" fill="#ffffff" opacity="0.9"/>
    <circle cx="60" cy="20" r="4" fill="#ffffff" opacity="0.9"/>
    <circle cx="120" cy="35" r="4" fill="#ffffff" opacity="0.9"/>
    <circle cx="180" cy="15" r="4" fill="#ffffff" opacity="0.9"/>
    <circle cx="240" cy="25" r="4" fill="#ffffff" opacity="0.9"/>
    <circle cx="300" cy="10" r="4" fill="#ffffff" opacity="0.9"/>
  </g>
  
  <!-- 装饰性元素 -->
  <circle cx="100" cy="100" r="8" fill="#ffffff" opacity="0.3"/>
  <circle cx="400" cy="120" r="6" fill="#ffffff" opacity="0.4"/>
  <circle cx="450" cy="400" r="10" fill="#ffffff" opacity="0.2"/>
  <circle cx="80" cy="450" r="5" fill="#ffffff" opacity="0.5"/>
</svg>