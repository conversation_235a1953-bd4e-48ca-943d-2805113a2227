# Currency Converter App

A React Native Expo app for currency conversion with real-time exchange rates and trend analysis.

## Features

### Currency Converter

- Real-time currency conversion
- Support for multiple currencies (USD, EUR, GBP, JPY, AUD, CNY, etc.)
- Last updated timestamp
- Common currencies quick selection

### Exchange Rate Trends (K-Line Chart)

- **Interactive K-Line (Candlestick) Charts** showing OHLC (Open, High, Low, Close) data
- Multiple time ranges (1 Week, 1 Month, 3 Months, 1 Year)
- Support for major currency pairs (USD/CNY, EUR/CNY, GBP/CNY, JPY/CNY, AUD/CNY)
- **Professional Trading Chart Display**:
  - Green candles for price increases (close ≥ open)
  - Red candles for price decreases (close < open)
  - Shadow lines showing high and low prices
  - Trend line overlay for overall direction
- Click on candlesticks to view detailed OHLC information
- Pull-to-refresh functionality
- Error handling with retry option
- Statistics display (Current, Highest, Lowest, Change percentage)

### Internationalization

- Support for English and Chinese languages
- Dynamic language switching
- Localized date and number formatting

### Technical Features

- Built with React Native and Expo
- TypeScript for type safety
- React Navigation for routing
- Custom K-Line chart implementation using React Native SVG
- React i18next for internationalization
- Tailwind CSS for styling
- Responsive design for different screen sizes

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Expo CLI

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd CurrencyConverter
```

2. Install dependencies:

```bash
npm install
```

3. Start the development server:

```bash
npm start
```

4. Run on your device:

- Scan the QR code with Expo Go app (iOS/Android)
- Or press 'i' for iOS simulator or 'a' for Android emulator

## Project Structure

```
CurrencyConverter/
├── app/                    # Expo Router pages
│   ├── (tabs)/           # Tab navigation
│   │   ├── index.tsx     # Home/Converter screen
│   │   ├── trend.tsx     # Trends screen with K-Line chart
│   │   └── me.tsx        # Profile screen
│   └── _layout.tsx       # Root layout
├── components/            # Reusable components
│   └── CandlestickChart.tsx  # Custom K-Line chart component
├── constants/            # App constants
├── hooks/               # Custom hooks
├── i18n/                # Internationalization
│   └── locales/         # Translation files
├── lib/                 # Utility functions
└── assets/              # Images and fonts
```

## Key Components

### Trends Screen (`app/(tabs)/trend.tsx`)

- **Currency Pair Selection**: Choose from 5 major currency pairs
- **Time Range Selection**: 1W, 1M, 3M, 1Y options
- **Interactive K-Line Charts**: Professional trading-style candlestick display
- **OHLC Data Display**: Shows Open, High, Low, Close values for selected data points
- **Statistics Panel**: Shows current, highest, lowest rates and change percentage
- **Pull-to-Refresh**: Swipe down to refresh data
- **Error Handling**: Graceful error display with retry functionality

### CandlestickChart Component (`components/CandlestickChart.tsx`)

- **Professional K-Line Rendering**: Custom implementation using React Native SVG
- **Interactive Data Points**: Click on candlesticks for detailed information
- **Color Coding**: Green for price increases, red for decreases
- **Grid Lines**: Horizontal price reference lines
- **Trend Line**: Dashed line showing overall price direction
- **Responsive Design**: Adapts to different screen sizes

### Features Implemented

- ✅ Professional K-Line (Candlestick) chart display
- ✅ OHLC (Open, High, Low, Close) data structure
- ✅ Interactive chart with data point selection
- ✅ Multiple time range support
- ✅ Currency pair switching
- ✅ Pull-to-refresh functionality
- ✅ Error handling and retry
- ✅ Internationalization support
- ✅ Responsive design
- ✅ Loading states
- ✅ Statistics calculation
- ✅ Trend line overlay
- ✅ Grid lines and price labels

## Technologies Used

- **React Native**: Cross-platform mobile development
- **Expo**: Development platform and tools
- **TypeScript**: Type safety and better development experience
- **React Navigation**: Navigation between screens
- **React Native SVG**: Custom chart rendering
- **React i18next**: Internationalization
- **Tailwind CSS**: Utility-first CSS framework

## Development

### Adding New Features

1. Create new components in the `components/` directory
2. Add new screens in the `app/` directory
3. Update translations in `i18n/locales/`
4. Test on both iOS and Android

### Code Style

- Use TypeScript for all new code
- Follow React Native best practices
- Use functional components with hooks
- Implement proper error handling
- Add loading states for async operations

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
