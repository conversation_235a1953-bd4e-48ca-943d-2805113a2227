import { Dimensions, PixelRatio } from 'react-native'

// 获取设备屏幕尺寸
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window')

// 设计稿基准尺寸 (通常以iPhone 6/7/8为基准: 375x667)
const DESIGN_WIDTH = 375
const DESIGN_HEIGHT = 667

/**
 * 根据设计稿宽度进行像素适配
 * @param size 设计稿中的尺寸
 * @returns 适配后的尺寸
 */
export const scaleWidth = (size: number): number => {
  return (SCREEN_WIDTH / DESIGN_WIDTH) * size
}

/**
 * 根据设计稿高度进行像素适配
 * @param size 设计稿中的尺寸
 * @returns 适配后的尺寸
 */
export const scaleHeight = (size: number): number => {
  return (SCREEN_HEIGHT / DESIGN_HEIGHT) * size
}

/**
 * 根据较小的缩放比例进行适配，保持比例不变形
 * @param size 设计稿中的尺寸
 * @returns 适配后的尺寸
 */
export const scaleSize = (size: number): number => {
  const scaleX = SCREEN_WIDTH / DESIGN_WIDTH
  const scaleY = SCREEN_HEIGHT / DESIGN_HEIGHT
  return Math.min(scaleX, scaleY) * size
}

/**
 * 字体大小适配，考虑设备像素密度
 * @param size 设计稿中的字体大小
 * @returns 适配后的字体大小
 */
export const scaleFontSize = (size: number): number => {
  const scale = SCREEN_WIDTH / DESIGN_WIDTH
  const newSize = size * scale
  // 使用PixelRatio.roundToNearestPixel确保在不同像素密度设备上显示清晰
  return Math.round(PixelRatio.roundToNearestPixel(newSize))
}

/**
 * 获取1像素线条宽度（在不同像素密度设备上显示为真正的1像素）
 * @returns 1像素线条宽度
 */
export const onePixel = (): number => {
  return 1 / PixelRatio.get()
}

/**
 * 根据像素密度适配尺寸
 * @param size 原始尺寸
 * @returns 适配后的尺寸
 */
export const scaleByPixelRatio = (size: number): number => {
  return PixelRatio.roundToNearestPixel(size)
}

/**
 * 判断是否为小屏设备（宽度小于350）
 * @returns 是否为小屏设备
 */
export const isSmallScreen = (): boolean => {
  return SCREEN_WIDTH < 350
}

/**
 * 判断是否为大屏设备（宽度大于400）
 * @returns 是否为大屏设备
 */
export const isLargeScreen = (): boolean => {
  return SCREEN_WIDTH > 400
}

/**
 * 获取设备信息
 * @returns 设备尺寸和像素密度信息
 */
export const getDeviceInfo = () => {
  return {
    screenWidth: SCREEN_WIDTH,
    screenHeight: SCREEN_HEIGHT,
    pixelRatio: PixelRatio.get(),
    fontScale: PixelRatio.getFontScale(),
    designWidth: DESIGN_WIDTH,
    designHeight: DESIGN_HEIGHT,
    scaleX: SCREEN_WIDTH / DESIGN_WIDTH,
    scaleY: SCREEN_HEIGHT / DESIGN_HEIGHT,
  }
}

/**
 * 响应式尺寸适配，根据屏幕大小返回不同的值
 * @param small 小屏幕值
 * @param medium 中等屏幕值
 * @param large 大屏幕值
 * @returns 适配后的值
 */
export const responsiveSize = (small: number, medium: number, large: number): number => {
  if (SCREEN_WIDTH < 350) {
    return small
  } else if (SCREEN_WIDTH < 400) {
    return medium
  } else {
    return large
  }
}

/**
 * 安全区域适配（考虑刘海屏等）
 * @param size 原始尺寸
 * @param safeAreaInsets 安全区域边距
 * @returns 适配后的尺寸
 */
export const scaleWithSafeArea = (size: number, safeAreaInsets = { top: 0, bottom: 0 }): number => {
  const availableHeight = SCREEN_HEIGHT - safeAreaInsets.top - safeAreaInsets.bottom
  const scale = availableHeight / (DESIGN_HEIGHT - safeAreaInsets.top - safeAreaInsets.bottom)
  return size * scale
}

// 导出常用的尺寸常量
export const DEVICE_INFO = getDeviceInfo()
export const ONE_PIXEL = onePixel()
export const IS_SMALL_SCREEN = isSmallScreen()
export const IS_LARGE_SCREEN = isLargeScreen()

// 简化的适配方法别名
export const wp = scaleWidth  // width percentage
export const hp = scaleHeight // height percentage
export const sp = scaleFontSize // scaled pixel for font
export const dp = scaleSize   // density independent pixel