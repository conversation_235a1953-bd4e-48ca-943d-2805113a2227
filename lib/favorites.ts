import AsyncStorage from '@react-native-async-storage/async-storage'

export interface FavoritePair {
  id: string
  base: string
  quote: string
  addedAt: string
}

const FAVORITES_STORAGE_KEY = 'favoritesCurrencyPairs'

export async function getFavorites(): Promise<FavoritePair[]> {
  try {
    const stored = await AsyncStorage.getItem(FAVORITES_STORAGE_KEY)
    if (!stored) return []
    
    const parsed = JSON.parse(stored)
    return Array.isArray(parsed) ? parsed : []
  } catch (error) {
    console.error('Error loading favorites:', error)
    return []
  }
}

export async function addFavorite(base: string, quote: string): Promise<FavoritePair[]> {
  try {
    const current = await getFavorites()
    const id = `${base}-${quote}`
    
    // 检查是否已存在
    if (current.some(fav => fav.id === id)) {
      return current // 已存在，不重复添加
    }
    
    const newFavorite: FavoritePair = {
      id,
      base,
      quote,
      addedAt: new Date().toISOString(),
    }
    
    const updated = [...current, newFavorite]
    await AsyncStorage.setItem(FAVORITES_STORAGE_KEY, JSON.stringify(updated))
    return updated
  } catch (error) {
    console.error('Error adding favorite:', error)
    return await getFavorites() // 返回当前列表
  }
}

export async function removeFavorite(pairId: string): Promise<FavoritePair[]> {
  try {
    const current = await getFavorites()
    const updated = current.filter(fav => fav.id !== pairId)
    await AsyncStorage.setItem(FAVORITES_STORAGE_KEY, JSON.stringify(updated))
    return updated
  } catch (error) {
    console.error('Error removing favorite:', error)
    return await getFavorites() // 返回当前列表
  }
}

export async function isFavorite(base: string, quote: string): Promise<boolean> {
  try {
    const favorites = await getFavorites()
    const id = `${base}-${quote}`
    return favorites.some(fav => fav.id === id)
  } catch (error) {
    console.error('Error checking favorite:', error)
    return false
  }
}

export async function toggleFavorite(base: string, quote: string): Promise<{ favorites: FavoritePair[]; isNowFavorite: boolean }> {
  const id = `${base}-${quote}`
  const isCurrentlyFavorite = await isFavorite(base, quote)
  
  if (isCurrentlyFavorite) {
    const favorites = await removeFavorite(id)
    return { favorites, isNowFavorite: false }
  } else {
    const favorites = await addFavorite(base, quote)
    return { favorites, isNowFavorite: true }
  }
}