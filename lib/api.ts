// Enhanced API utility with better error handling and retry logic

export interface ApiError {
  code: string
  message: string
  details?: any
}

export class AppApiError extends Error {
  code: string
  details?: any

  constructor(code: string, message: string, details?: any) {
    super(message)
    this.name = 'AppApiError'
    this.code = code
    this.details = details
  }
}

interface RetryOptions {
  maxRetries?: number
  retryDelay?: number
  backoffMultiplier?: number
}

export async function fetchWithRetry(
  url: string,
  options: RequestInit = {},
  retryOptions: RetryOptions = {}
): Promise<Response> {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    backoffMultiplier = 2,
  } = retryOptions

  let lastError: Error | null = null

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      })

      if (!response.ok) {
        throw new AppApiError(
          `HTTP_${response.status}`,
          `Request failed with status ${response.status}`,
          { status: response.status, statusText: response.statusText }
        )
      }

      return response
    } catch (error) {
      lastError = error as Error
      
      // Don't retry on client errors (4xx) except for 429 (Rate Limited)
      if (error instanceof AppApiError && 
          error.code.startsWith('HTTP_4') && 
          error.code !== 'HTTP_429') {
        throw error
      }

      // If this is the last attempt, throw the error
      if (attempt === maxRetries) {
        break
      }

      // Wait before retrying
      const delay = retryDelay * Math.pow(backoffMultiplier, attempt)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  // Transform network errors into our custom error type
  if (lastError) {
    if (lastError.name === 'TypeError' && lastError.message.includes('fetch')) {
      throw new AppApiError(
        'NETWORK_ERROR',
        'Network connection failed. Please check your internet connection.',
        { originalError: lastError }
      )
    }
    
    if (lastError instanceof AppApiError) {
      throw lastError
    }
    
    throw new AppApiError(
      'UNKNOWN_ERROR',
      'An unexpected error occurred',
      { originalError: lastError }
    )
  }

  throw new AppApiError('MAX_RETRIES_EXCEEDED', 'Maximum retry attempts exceeded')
}

export interface ExchangeRateResponse {
  rates: Record<string, number>
  date: string
  base: string
}

export async function fetchExchangeRate(
  fromCurrency: string,
  toCurrency: string
): Promise<ExchangeRateResponse> {
  if (fromCurrency === toCurrency) {
    return {
      rates: { [toCurrency]: 1 },
      date: new Date().toISOString().split('T')[0],
      base: fromCurrency,
    }
  }

  try {
    const url = `https://api.frankfurter.app/latest?from=${fromCurrency}&to=${toCurrency}`
    const response = await fetchWithRetry(url, {}, { maxRetries: 2 })
    const data = await response.json()
    
    if (!data.rates || typeof data.rates[toCurrency] !== 'number') {
      throw new AppApiError(
        'INVALID_RESPONSE',
        'Invalid exchange rate data received',
        { data }
      )
    }
    
    return data as ExchangeRateResponse
  } catch (error) {
    if (error instanceof AppApiError) {
      throw error
    }
    
    throw new AppApiError(
      'EXCHANGE_RATE_ERROR',
      'Failed to fetch exchange rate',
      { originalError: error }
    )
  }
}

export interface TimeSeriesData {
  base: string
  start_date: string
  end_date: string
  rates: {
    [date: string]: {
      [currency: string]: number
    }
  }
}

export async function fetchTimeSeriesData(
  baseCurrency: string,
  targetCurrency: string,
  startDate: string,
  endDate: string
): Promise<TimeSeriesData> {
  try {
    const url = `https://api.frankfurter.dev/v1/${startDate}..${endDate}?base=${baseCurrency}&symbols=${targetCurrency}`
    const response = await fetchWithRetry(url, {}, { maxRetries: 2 })
    const data = await response.json()
    
    if (!data.rates || Object.keys(data.rates).length === 0) {
      throw new AppApiError(
        'NO_DATA',
        'No exchange rate data available for the selected period',
        { data }
      )
    }
    
    return data as TimeSeriesData
  } catch (error) {
    if (error instanceof AppApiError) {
      throw error
    }
    
    throw new AppApiError(
      'TIME_SERIES_ERROR',
      'Failed to fetch time series data',
      { originalError: error }
    )
  }
}