import AsyncStorage from '@react-native-async-storage/async-storage';

export interface AppSettings {
  defaultCurrency: string;
  themeMode: 'system' | 'light' | 'dark';
  language: string;
}

const SETTINGS_KEY = 'APP_SETTINGS';

const defaultSettings: AppSettings = {
  defaultCurrency: 'CNY',
  themeMode: 'system',
  language: 'zh',
};

export async function getSettings(): Promise<AppSettings> {
  try {
    const storedSettings = await AsyncStorage.getItem(SETTINGS_KEY);
    if (storedSettings) {
      return { ...defaultSettings, ...JSON.parse(storedSettings) };
    }
  } catch (error) {
    console.warn('Failed to load settings:', error);
  }
  return defaultSettings;
}

export async function updateSettings(settings: Partial<AppSettings>): Promise<void> {
  try {
    const currentSettings = await getSettings();
    const newSettings = { ...currentSettings, ...settings };
    await AsyncStorage.setItem(SETTINGS_KEY, JSON.stringify(newSettings));
  } catch (error) {
    console.warn('Failed to save settings:', error);
  }
}

export async function getSetting<K extends keyof AppSettings>(key: K): Promise<AppSettings[K]> {
  const settings = await getSettings();
  return settings[key];
}

export async function setSetting<K extends keyof AppSettings>(
  key: K,
  value: AppSettings[K]
): Promise<void> {
  await updateSettings({ [key]: value } as Partial<AppSettings>);
}