<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>货币转换应用原型</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #4A6FFF;
            --secondary-color: #6C84FF;
            --accent-color: #FF9F43;
            --background-color: #F8F9FA;
            --card-color: #FFFFFF;
            --text-primary: #2D3748;
            --text-secondary: #718096;
            --border-color: #E2E8F0;
            --success-color: #48BB78;
            --error-color: #F56565;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        body {
            background-color: #F0F2F5;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        h1 {
            margin-bottom: 30px;
            color: var(--text-primary);
            text-align: center;
        }

        .prototype-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .screen {
            width: 375px;
            height: 812px;
            background-color: var(--background-color);
            border-radius: 40px;
            overflow: hidden;
            position: relative;
            box-shadow: var(--shadow);
            border: 10px solid #000;
        }

        .screen-title {
            text-align: center;
            margin-top: 10px;
            margin-bottom: 20px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .status-bar {
            height: 44px;
            background-color: var(--background-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .time {
            font-weight: 600;
            font-size: 14px;
        }

        .status-icons {
            display: flex;
            gap: 5px;
        }

        .content {
            height: calc(100% - 44px - 83px);
            overflow-y: auto;
            background-color: var(--background-color);
            padding: 20px;
        }

        .tab-bar {
            height: 83px;
            background-color: var(--card-color);
            position: absolute;
            bottom: 0;
            width: 100%;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
            border-top: 1px solid var(--border-color);
        }

        .tab {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: var(--text-secondary);
            font-size: 10px;
        }

        .tab.active {
            color: var(--primary-color);
        }

        .tab i {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .header h2 {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
        }

        .card {
            background-color: var(--card-color);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--shadow);
        }

        .currency-input {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }

        .currency-flag {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: var(--background-color);
            margin-right: 12px;
            font-size: 20px;
        }

        .currency-details {
            flex: 1;
        }

        .currency-code {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 16px;
        }

        .currency-name {
            color: var(--text-secondary);
            font-size: 12px;
        }

        .currency-amount {
            font-weight: 700;
            font-size: 24px;
            color: var(--text-primary);
            text-align: right;
        }

        .swap-button {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            margin: 0 auto;
            margin-bottom: 16px;
            box-shadow: var(--shadow);
        }

        .convert-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px;
            width: 100%;
            font-weight: 600;
            font-size: 16px;
            margin-top: 20px;
            cursor: pointer;
        }

        .rate-info {
            text-align: center;
            color: var(--text-secondary);
            font-size: 14px;
            margin-top: 12px;
        }

        .currency-list {
            list-style: none;
        }

        .currency-item {
            display: flex;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .currency-item:last-child {
            border-bottom: none;
        }

        .search-bar {
            display: flex;
            align-items: center;
            background-color: var(--card-color);
            border-radius: 12px;
            padding: 12px 16px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
        }

        .search-bar i {
            color: var(--text-secondary);
            margin-right: 12px;
        }

        .search-bar input {
            border: none;
            background: transparent;
            flex: 1;
            font-size: 16px;
            color: var(--text-primary);
            outline: none;
        }

        .search-bar input::placeholder {
            color: var(--text-secondary);
        }

        .favorite {
            color: var(--accent-color);
        }

        .chart-container {
            width: 100%;
            height: 200px;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }

        .chart {
            width: 100%;
            height: 100%;
            background-image: linear-gradient(to bottom, rgba(74, 111, 255, 0.2), rgba(74, 111, 255, 0));
            position: relative;
        }

        .chart::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='none' stroke='%234A6FFF' stroke-width='2' d='M0,224L60,213.3C120,203,240,181,360,181.3C480,181,600,203,720,213.3C840,224,960,224,1080,208C1200,192,1320,160,1380,144L1440,128L1440,0L1380,0C1320,0,1200,0,1080,0C960,0,840,0,720,0C600,0,480,0,360,0C240,0,120,0,60,0L0,0Z'%3E%3C/path%3E%3C/svg%3E");
            background-size: cover;
            background-position: center;
        }

        .time-filter {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .time-option {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .time-option.active {
            background-color: var(--primary-color);
            color: white;
        }

        .stats-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .stat-card {
            background-color: var(--card-color);
            border-radius: 12px;
            padding: 16px;
            width: 48%;
            box-shadow: var(--shadow);
        }

        .stat-title {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .stat-value {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-primary);
        }

        .stat-change {
            font-size: 12px;
            font-weight: 600;
        }

        .positive {
            color: var(--success-color);
        }

        .negative {
            color: var(--error-color);
        }

        .notification {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            padding: 16px;
            background-color: var(--card-color);
            border-radius: 12px;
            box-shadow: var(--shadow);
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(74, 111, 255, 0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--primary-color);
            margin-right: 16px;
            font-size: 16px;
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .notification-message {
            color: var(--text-secondary);
            font-size: 14px;
        }

        .notification-time {
            color: var(--text-secondary);
            font-size: 12px;
            white-space: nowrap;
            margin-left: 16px;
        }

        .settings-section {
            margin-bottom: 24px;
        }

        .settings-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 16px;
            text-transform: uppercase;
        }

        .settings-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            background-color: var(--card-color);
            border-radius: 12px;
            margin-bottom: 12px;
            box-shadow: var(--shadow);
        }

        .settings-item-left {
            display: flex;
            align-items: center;
        }

        .settings-icon {
            width: 36px;
            height: 36px;
            border-radius: 10px;
            background-color: rgba(74, 111, 255, 0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--primary-color);
            margin-right: 16px;
        }

        .settings-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .toggle {
            width: 50px;
            height: 28px;
            background-color: var(--success-color);
            border-radius: 14px;
            position: relative;
            cursor: pointer;
        }

        .toggle::before {
            content: '';
            position: absolute;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: white;
            top: 2px;
            right: 2px;
            transition: all 0.3s;
        }

        .toggle.off {
            background-color: var(--border-color);
        }

        .toggle.off::before {
            right: 24px;
        }

        .profile-header {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-color: var(--secondary-color);
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 32px;
            margin-right: 16px;
        }

        .profile-info h3 {
            font-size: 20px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .profile-info p {
            color: var(--text-secondary);
            font-size: 14px;
        }

        .edit-profile-button {
            background-color: var(--background-color);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 12px 16px;
            width: 100%;
            font-weight: 500;
            font-size: 16px;
            color: var(--text-primary);
            margin-bottom: 24px;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .edit-profile-button i {
            margin-right: 8px;
        }

        .transaction {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .transaction:last-child {
            border-bottom: none;
        }

        .transaction-left {
            display: flex;
            align-items: center;
        }

        .transaction-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 16px;
            font-size: 20px;
        }

        .transaction-icon.sent {
            background-color: rgba(245, 101, 101, 0.1);
            color: var(--error-color);
        }

        .transaction-icon.received {
            background-color: rgba(72, 187, 120, 0.1);
            color: var(--success-color);
        }

        .transaction-details h4 {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .transaction-details p {
            color: var(--text-secondary);
            font-size: 12px;
        }

        .transaction-amount {
            font-weight: 700;
            font-size: 16px;
        }

        .transaction-amount.sent {
            color: var(--error-color);
        }

        .transaction-amount.received {
            color: var(--success-color);
        }

        .onboarding-screen {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 100%;
            padding: 40px 20px;
        }

        .onboarding-image {
            width: 100%;
            height: 300px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 40px;
        }

        .onboarding-image i {
            font-size: 150px;
            color: var(--primary-color);
        }

        .onboarding-content h2 {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 16px;
            text-align: center;
        }

        .onboarding-content p {
            color: var(--text-secondary);
            font-size: 16px;
            text-align: center;
            margin-bottom: 40px;
            line-height: 1.5;
        }

        .onboarding-dots {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
        }

        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--border-color);
            margin: 0 4px;
        }

        .dot.active {
            background-color: var(--primary-color);
            width: 20px;
            border-radius: 4px;
        }

        .onboarding-buttons {
            display: flex;
            justify-content: space-between;
        }

        .skip-button {
            background-color: transparent;
            border: none;
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 16px;
            cursor: pointer;
        }

        .next-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px 32px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
        }

        .login-form {
            margin-top: 40px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input {
            width: 100%;
            padding: 16px;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            font-size: 16px;
            color: var(--text-primary);
            background-color: var(--card-color);
        }

        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }

        .forgot-password {
            text-align: right;
            margin-bottom: 24px;
        }

        .forgot-password a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .social-login {
            margin-top: 40px;
            text-align: center;
        }

        .social-login-text {
            color: var(--text-secondary);
            margin-bottom: 16px;
            position: relative;
        }

        .social-login-text::before,
        .social-login-text::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 30%;
            height: 1px;
            background-color: var(--border-color);
        }

        .social-login-text::before {
            left: 0;
        }

        .social-login-text::after {
            right: 0;
        }

        .social-buttons {
            display: flex;
            justify-content: center;
            gap: 16px;
        }

        .social-button {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            background-color: var(--card-color);
            border: 1px solid var(--border-color);
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 24px;
            color: var(--text-primary);
        }

        .signup-link {
            text-align: center;
            margin-top: 40px;
            color: var(--text-secondary);
        }

        .signup-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .alert {
            padding: 16px;
            border-radius: 12px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }

        .alert i {
            margin-right: 12px;
            font-size: 20px;
        }

        .alert-success {
            background-color: rgba(72, 187, 120, 0.1);
            color: var(--success-color);
        }

        .alert-error {
            background-color: rgba(245, 101, 101, 0.1);
            color: var(--error-color);
        }

        .alert-warning {
            background-color: rgba(255, 159, 67, 0.1);
            color: var(--accent-color);
        }
    </style>
</head>
<body>
    <h1>货币转换应用原型</h1>

    <div class="prototype-container">
        <!-- 欢迎页 / 引导页 1 -->
        <div>
            <h2 class="screen-title">欢迎页 / 引导页 1</h2>
            <div class="screen">
                <div class="status-bar">
                    <div class="time">9:41</div>
                    <div class="status-icons">
                        <i class="fas fa-signal"></i>
                        <i class="fas fa-wifi"></i>
                        <i class="fas fa-battery-full"></i>
                    </div>
                </div>
                <div class="onboarding-screen">
                    <div>
                        <div class="onboarding-image">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="onboarding-content">
                            <h2>实时货币转换</h2>
                            <p>随时随地获取最新汇率，轻松转换全球160多种货币</p>
                        </div>
                    </div>
                    <div>
                        <div class="onboarding-dots">
                            <div class="dot active"></div>
                            <div class="dot"></div>
                            <div class="dot"></div>
                        </div>
                        <div class="onboarding-buttons">
                            <button class="skip-button">跳过</button>
                            <button class="next-button">下一步</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 引导页 2 -->
        <div>
            <h2 class="screen-title">引导页 2</h2>
            <div class="screen">
                <div class="status-bar">
                    <div class="time">9:41</div>
                    <div class="status-icons">
                        <i class="fas fa-signal"></i>
                        <i class="fas fa-wifi"></i>
                        <i class="fas fa-battery-full"></i>
                    </div>
                </div>
                <div class="onboarding-screen">
                    <div>
                        <div class="onboarding-image">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="onboarding-content">
                            <h2>汇率走势分析</h2>
                            <p>查看历史汇率走势，分析市场变化，把握最佳兑换时机</p>
                        </div>
                    </div>
                    <div>
                        <div class="onboarding-dots">
                            <div class="dot"></div>
                            <div class="dot active"></div>
                            <div class="dot"></div>
                        </div>
                        <div class="onboarding-buttons">
                            <button class="skip-button">跳过</button>
                            <button class="next-button">下一步</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 引导页 3 -->
        <div>
            <h2 class="screen-title">引导页 3</h2>
            <div class="screen">
                <div class="status-bar">
                    <div class="time">9:41</div>
                    <div class="status-icons">
                        <i class="fas fa-signal"></i>
                        <i class="fas fa-wifi"></i>
                        <i class="fas fa-battery-full"></i>
                    </div>
                </div>
                <div class="onboarding-screen">
                    <div>
                        <div class="onboarding-image">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="onboarding-content">
                            <h2>汇率提醒</h2>
                            <p>设置汇率提醒，当汇率达到您设定的目标时，立即收到通知</p>
                        </div>
                    </div>
                    <div>
                        <div class="onboarding-dots">
                            <div class="dot"></div>
                            <div class="dot"></div>
                            <div class="dot active"></div>
                        </div>
                        <div class="onboarding-buttons">
                            <button class="skip-button">跳过</button>
                            <button class="next-button">开始使用</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 登录页面 -->
        <div>
            <h2 class="screen-title">登录页面</h2>
            <div class="screen">
                <div class="status-bar">
                    <div class="time">9:41</div>
                    <div class="status-icons">
                        <i class="fas fa-signal"></i>
                        <i class="fas fa-wifi"></i>
                        <i class="fas fa-battery-full"></i>
                    </div>
                </div>
                <div class="content">
                    <div class="header">
                        <h2>欢迎回来</h2>
                    </div>
                    
                    <div class="login-form">
                        <div class="form-group">
                            <label class="form-label">邮箱</label>
                            <input type="email" class="form-input" placeholder="请输入您的邮箱">
                        </div>
                        <div class="form-group">
                            <label class="form-label">密码</label>
                            <input type="password" class="form-input" placeholder="请输入您的密码">
                        </div>
                        <div class="forgot-password">
                            <a href="#">忘记密码？</a>
                        </div>
                        <button class="convert-button">登录</button>
                    </div>

                    <div class="social-login">
                        <div class="social-login-text">或使用社交账号登录</div>
                        <div class="social-buttons">
                            <div class="social-button">
                                <i class="fab fa-google"></i>
                            </div>
                            <div class="social-button">
                                <i class="fab fa-apple"></i>
                            </div>
                            <div class="social-button">
                                <i class="fab fa-facebook-f"></i>
                            </div>
                        </div>
                    </div>

                    <div class="signup-link">
                        还没有账号？ <a href="#">立即注册</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 注册页面 -->
        <div>
            <h2 class="screen-title">注册页面</h2>
            <div class="screen">
                <div class="status-bar">
                    <div class="time">9:41</div>
                    <div class="status-icons">
                        <i class="fas fa-signal"></i>
                        <i class="fas fa-wifi"></i>
                        <i class="fas fa-battery-full"></i>
                    </div>
                </div>
                <div class="content">
                    <div class="header">
                        <h2>创建账号</h2>
                    </div>
                    
                    <div class="login-form">
                        <div class="form-group">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-input" placeholder="请输入您的用户名">
                        </div>
                        <div class="form-group">
                            <label class="form-label">邮箱</label>
                            <input type="email" class="form-input" placeholder="请输入您的邮箱">
                        </div>
                        <div class="form-group">
                            <label class="form-label">密码</label>
                            <input type="password" class="form-input" placeholder="请输入您的密码">
                        </div>
                        <div class="form-group">
                            <label class="form-label">确认密码</label>
                            <input type="password" class="form-input" placeholder="请再次输入您的密码">
                        </div>
                        <button class="convert-button">注册</button>
                    </div>

                    <div class="signup-link">
                        已有账号？ <a href="#">立即登录</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主页/转换页面 -->
        <div>
            <h2 class="screen-title">主页/转换页面</h2>
            <div class="screen">
                <div class="status-bar">
                    <div class="time">9:41</div>
                    <div class="status-icons">
                        <i class="fas fa-signal"></i>
                        <i class="fas fa-wifi"></i>
                        <i class="fas fa-battery-full"></i>
                    </div>
                </div>
                <div class="content">
                    <div class="header">
                        <h2>货币转换</h2>
                        <i class="fas fa-cog" style="font-size: 24px; color: var(--text-secondary);"></i>
                    </div>
                    
                    <div class="card">
                        <div class="currency-input">
                            <div class="currency-flag">
                                <span>🇨🇳</span>
                            </div>
                            <div class="currency-details">
                                <div class="currency-code">CNY</div>
                                <div class="currency-name">人民币</div>
                            </div>
                            <div class="currency-amount">100.00</div>
                        </div>
                        
                        <div class="swap-button">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        
                        <div class="currency-input">
                            <div class="currency-flag">
                                <span>🇺🇸</span>
                            </div>
                            <div class="currency-details">
                                <div class="currency-code">USD</div>
                                <div class="currency-name">美元</div>
                            </div>
                            <div class="currency-amount">13.81</div>
                        </div>
                        
                        <button class="convert-button">转换</button>
                        
                        <div class="rate-info">
                            1 CNY = 0.1381 USD • 更新于 1分钟前
                        </div>
                    </div>
                    
                    <div class="header" style="margin-top: 24px;">
                        <h2>常用货币</h2>
                        <span style="color: var(--primary-color); font-weight: 500;">查看全部</span>
                    </div>
                    
                    <div class="card">
                        <ul class="currency-list">
                            <li class="currency-item">
                                <div class="currency-flag">
                                    <span>🇺🇸</span>
                                </div>
                                <div class="currency-details">
                                    <div class="currency-code">USD</div>
                                    <div class="currency-name">美元</div>
                                </div>
                                <div style="flex: 1; text-align: right;">
                                    <div class="currency-code">7.24</div>
                                    <div class="currency-name positive">+0.02%</div>
                                </div>
                                <i class="fas fa-star favorite" style="margin-left: 16px;"></i>
                            </li>
                            <li class="currency-item">
                                <div class="currency-flag">
                                    <span>🇪🇺</span>
                                </div>
                                <div class="currency-details">
                                    <div class="currency-code">EUR</div>
                                    <div class="currency-name">欧元</div>
                                </div>
                                <div style="flex: 1; text-align: right;">
                                    <div class="currency-code">7.86</div>
                                    <div class="currency-name negative">-0.15%</div>
                                </div>
                                <i class="fas fa-star favorite" style="margin-left: 16px;"></i>
                            </li>
                            <li class="currency-item">
                                <div class="currency-flag">
                                    <span>🇬🇧</span>
                                </div>
                                <div class="currency-details">
                                    <div class="currency-code">GBP</div>
                                    <div class="currency-name">英镑</div>
                                </div>
                                <div style="flex: 1; text-align: right;">
                                    <div class="currency-code">9.21</div>
                                    <div class="currency-name positive">+0.08%</div>
                                </div>
                                <i class="fas fa-star favorite" style="margin-left: 16px;"></i>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="tab-bar">
                    <div class="tab active">
                        <i class="fas fa-exchange-alt"></i>
                        <span>转换</span>
                    </div>
                    <div class="tab">
                        <i class="fas fa-chart-line"></i>
                        <span>走势</span>
                    </div>
                    <div class="tab">
                        <i class="fas fa-bell"></i>
                        <span>提醒</span>
                    </div>
                    <div class="tab">
                        <i class="fas fa-user"></i>
                        <span>我的</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 货币列表页面 -->
        <div>
            <h2 class="screen-title">货币列表页面</h2>
            <div class="screen">
                <div class="status-bar">
                    <div class="time">9:41</div>
                    <div class="status-icons">
                        <i class="fas fa-signal"></i>
                        <i class="fas fa-wifi"></i>
                        <i class="fas fa-battery-full"></i>
                    </div>
                </div>
                <div class="content">
                    <div class="header">
                        <h2>选择货币</h2>
                        <i class="fas fa-times" style="font-size: 24px; color: var(--text-secondary);"></i>
                    </div>
                    
                    <div class="search-bar">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索货币名称或代码">
                    </div>
                    
                    <div class="card">
                        <ul class="currency-list">
                            <li class="currency-item">
                                <div class="currency-flag">
                                    <span>🇨🇳</span>
                                </div>
                                <div class="currency-details">
                                    <div class="currency-code">CNY</div>
                                    <div class="currency-name">人民币</div>
                                </div>
                                <i class="fas fa-check" style="color: var(--primary-color);"></i>
                            </li>
                            <li class="currency-item">
                                <div class="currency-flag">
                                    <span>🇺🇸</span>
                                </div>
                                <div class="currency-details">
                                    <div class="currency-code">USD</div>
                                    <div class="currency-name">美元</div>
                                </div>
                                <i class="fas fa-star favorite"></i>
                            </li>
                            <li class="currency-item">
                                <div class="currency-flag">
                                    <span>🇪🇺</span>
                                </div>
                                <div class="currency-details">
                                    <div class="currency-code">EUR</div>
                                    <div class="currency-name">欧元</div>
                                </div>
                                <i class="fas fa-star favorite"></i>
                            </li>
                            <li class="currency-item">
                                <div class="currency-flag">
                                    <span>🇬🇧</span>
                                </div>
                                <div class="currency-details">
                                    <div class="currency-code">GBP</div>
                                    <div class="currency-name">英镑</div>
                                </div>
                                <i class="fas fa-star favorite"></i>
                            </li>
                            <li class="currency-item">
                                <div class="currency-flag">
                                    <span>🇯🇵</span>
                                </div>
                                <div class="currency-details">
                                    <div class="currency-code">JPY</div>
                                    <div class="currency-name">日元</div>
                                </div>
                                <i class="far fa-star" style="color: var(--text-secondary);"></i>
                            </li>
                            <li class="currency-item">
                                <div class="currency-flag">
                                    <span>🇰🇷</span>
                                </div>
                                <div class="currency-details">
                                    <div class="currency-code">KRW</div>
                                    <div class="currency-name">韩元</div>
                                </div>
                                <i class="far fa-star" style="color: var(--text-secondary);"></i>
                            </li>
                            <li class="currency-item">
                                <div class="currency-flag">
                                    <span>🇦🇺</span>
                                </div>
                                <div class="currency-details">
                                    <div class="currency-code">AUD</div>
                                    <div class="currency-name">澳元</div>
                                </div>
                                <i class="far fa-star" style="color: var(--text-secondary);"></i>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 汇率走势页面 -->
        <div>
            <h2 class="screen-title">汇率走势页面</h2>
            <div class="screen">
                <div class="status-bar">
                    <div class="time">9:41</div>
                    <div class="status-icons">
                        <i class="fas fa-signal"></i>
                        <i class="fas fa-wifi"></i>
                        <i class="fas fa-battery-full"></i>
                    </div>
                </div>
                <div class="content">
                    <div class="header">
                        <h2>汇率走势</h2>
                    </div>
                    
                    <div class="card">
                        <div class="currency-input" style="margin-bottom: 24px;">
                            <div class="currency-flag">
                                <span>🇨🇳</span>
                            </div>
                            <div class="currency-details">
                                <div class="currency-code">CNY / USD</div>
                                <div class="currency-name">人民币 / 美元</div>
                            </div>
                            <div class="currency-amount">0.1381</div>
                        </div>
                        
                        <div class="time-filter">
                            <div class="time-option">1D</div>
                            <div class="time-option">1W</div>
                            <div class="time-option active">1M</div>
                            <div class="time-option">3M</div>
                            <div class="time-option">1Y</div>
                            <div class="time-option">5Y</div>
                        </div>
                        
                        <div class="chart-container">
                            <div class="chart"></div>
                        </div>
                        
                        <div class="stats-row">
                            <div class="stat-card">
                                <div class="stat-title">最高</div>
                                <div class="stat-value">0.1392</div>
                                <div class="stat-change positive">+0.80%</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-title">最低</div>
                                <div class="stat-value">0.1375</div>
                                <div class="stat-change negative">-0.43%</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="header" style="margin-top: 24px;">
                        <h2>相关货币对</h2>
                    </div>
                    
                    <div class="card">
                        <ul class="currency-list">
                            <li class="currency-item">
                                <div class="currency-flag">
                                    <span>🇪🇺</span>
                                </div>
                                <div class="currency-details">
                                    <div class="currency-code">CNY / EUR</div>
                                    <div class="currency-name">人民币 / 欧元</div>
                                </div>
                                <div style="flex: 1; text-align: right;">
                                    <div class="currency-code">0.1272</div>
                                    <div class="currency-name negative">-0.15%</div>
                                </div>
                            </li>
                            <li class="currency-item">
                                <div class="currency-flag">
                                    <span>🇬🇧</span>
                                </div>
                                <div class="currency-details">
                                    <div class="currency-code">CNY / GBP</div>
                                    <div class="currency-name">人民币 / 英镑</div>
                                </div>
                                <div style="flex: 1; text-align: right;">
                                    <div class="currency-code">0.1086</div>
                                    <div class="currency-name positive">+0.08%</div>
                                </div>
                            </li>
                            <li class="currency-item">
                                <div class="currency-flag">
                                    <span>🇯🇵</span>
                                </div>
                                <div class="currency-details">
                                    <div class="currency-code">CNY / JPY</div>
                                    <div class="currency-name">人民币 / 日元</div>
                                </div>
                                <div style="flex: 1; text-align: right;">
                                    <div class="currency-code">20.9753</div>
                                    <div class="currency-name positive">+0.22%</div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="tab-bar">
                    <div class="tab">
                        <i class="fas fa-exchange-alt"></i>
                        <span>转换</span>
                    </div>
                    <div class="tab active">
                        <i class="fas fa-chart-line"></i>
                        <span>走势</span>
                    </div>
                    <div class="tab">
                        <i class="fas fa-bell"></i>
                        <span>提醒</span>
                    </div>
                    <div class="tab">
                        <i class="fas fa-user"></i>
                        <span>我的</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 汇率提醒页面 -->
        <div>
            <h2 class="screen-title">汇率提醒页面</h2>
            <div class="screen">
                <div class="status-bar">
                    <div class="time">9:41</div>
                    <div class="status-icons">
                        <i class="fas fa-signal"></i>
                        <i class="fas fa-wifi"></i>
                        <i class="fas fa-battery-full"></i>
                    </div>
                </div>
                <div class="content">
                    <div class="header">
                        <h2>汇率提醒</h2>
                        <i class="fas fa-plus" style="font-size: 24px; color: var(--primary-color);"></i>
                    </div>
                    
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <div>您已成功设置汇率提醒</div>
                    </div>
                    
                    <div class="card">
                        <div class="currency-input" style="margin-bottom: 0;">
                            <div class="currency-flag">
                                <span>🇺🇸</span>
                            </div>
                            <div class="currency-details">
                                <div class="currency-code">USD / CNY</div>
                                <div class="currency-name">美元 / 人民币</div>
                            </div>
                            <div>
                                <div class="currency-amount">7.24</div>
                                <div style="text-align: right; color: var(--text-secondary); font-size: 12px;">当前汇率</div>
                            </div>
                        </div>
                        
                        <div style="height: 1px; background-color: var(--border-color); margin: 16px 0;"></div>
                        
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 600; color: var(--text-primary); margin-bottom: 4px;">目标汇率</div>
                                <div style="color: var(--text-secondary); font-size: 12px;">当汇率 ≥ 7.30 时通知我</div>
                            </div>
                            <div class="toggle"></div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="currency-input" style="margin-bottom: 0;">
                            <div class="currency-flag">
                                <span>🇪🇺</span>
                            </div>
                            <div class="currency-details">
                                <div class="currency-code">EUR / CNY</div>
                                <div class="currency-name">欧元 / 人民币</div>
                            </div>
                            <div>
                                <div class="currency-amount">7.86</div>
                                <div style="text-align: right; color: var(--text-secondary); font-size: 12px;">当前汇率</div>
                            </div>
                        </div>
                        
                        <div style="height: 1px; background-color: var(--border-color); margin: 16px 0;"></div>
                        
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 600; color: var(--text-primary); margin-bottom: 4px;">目标汇率</div>
                                <div style="color: var(--text-secondary); font-size: 12px;">当汇率 ≤ 7.75 时通知我</div>
                            </div>
                            <div class="toggle"></div>
                        </div>
                    </div>
                    
                    <div class="header" style="margin-top: 24px;">
                        <h2>历史提醒</h2>
                    </div>
                    
                    <div class="notification">
                        <div class="notification-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-title">USD/CNY 已达到目标汇率</div>
                            <div class="notification-message">美元兑人民币汇率已达到 7.30</div>
                        </div>
                        <div class="notification-time">昨天</div>
                    </div>
                    
                    <div class="notification">
                        <div class="notification-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-title">GBP/CNY 已达到目标汇率</div>
                            <div class="notification-message">英镑兑人民币汇率已达到 9.25</div>
                        </div>
                        <div class="notification-time">3天前</div>
                    </div>
                </div>
                <div class="tab-bar">
                    <div class="tab">
                        <i class="fas fa-exchange-alt"></i>
                        <span>转换</span>
                    </div>
                    <div class="tab">
                        <i class="fas fa-chart-line"></i>
                        <span>走势</span>
                    </div>
                    <div class="tab active">
                        <i class="fas fa-bell"></i>
                        <span>提醒</span>
                    </div>
                    <div class="tab">
                        <i class="fas fa-user"></i>
                        <span>我的</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加提醒页面 -->
        <div>
            <h2 class="screen-title">添加提醒页面</h2>
            <div class="screen">
                <div class="status-bar">
                    <div class="time">9:41</div>
                    <div class="status-icons">
                        <i class="fas fa-signal"></i>
                        <i class="fas fa-wifi"></i>
                        <i class="fas fa-battery-full"></i>
                    </div>
                </div>
                <div class="content">
                    <div class="header">
                        <h2>添加提醒</h2>
                        <i class="fas fa-times" style="font-size: 24px; color: var(--text-secondary);"></i>
                    </div>
                    
                    <div class="card">
                        <div class="form-group">
                            <label class="form-label">选择货币对</label>
                            <div class="currency-input" style="border: 1px solid var(--border-color); border-radius: 12px; padding: 12px;">
                                <div class="currency-flag">
                                    <span>🇬🇧</span>
                                </div>
                                <div class="currency-details">
                                    <div class="currency-code">GBP / CNY</div>
                                    <div class="currency-name">英镑 / 人民币</div>
                                </div>
                                <i class="fas fa-chevron-down" style="color: var(--text-secondary);"></i>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">当前汇率</label>
                            <div style="font-weight: 700; font-size: 24px; color: var(--text-primary);">9.21</div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">提醒条件</label>
                            <div style="display: flex; gap: 12px;">
                                <div style="flex: 1; padding: 12px; border: 1px solid var(--primary-color); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: var(--primary-color); font-weight: 500;">
                                    <i class="fas fa-arrow-up" style="margin-right: 8px;"></i>
                                    上涨至
                                </div>
                                <div style="flex: 1; padding: 12px; border: 1px solid var(--border-color); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: var(--text-secondary); font-weight: 500;">
                                    <i class="fas fa-arrow-down" style="margin-right: 8px;"></i>
                                    下跌至
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">目标汇率</label>
                            <input type="text" class="form-input" placeholder="请输入目标汇率" value="9.30">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">提醒方式</label>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="font-weight: 500; color: var(--text-primary);">邮件通知</div>
                                <div class="toggle off"></div>
                            </div>
                        </div>
                        
                        <button class="convert-button">保存提醒</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 个人中心页面 -->
        <div>
            <h2 class="screen-title">个人中心页面</h2>
            <div class="screen">
                <div class="status-bar">
                    <div class="time">9:41</div>
                    <div class="status-icons">
                        <i class="fas fa-signal"></i>
                        <i class="fas fa-wifi"></i>
                        <i class="fas fa-battery-full"></i>
                    </div>
                </div>
                <div class="content">
                    <div class="profile-header">
                        <div class="profile-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="profile-info">
                            <h3>张三</h3>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    
                    <button class="edit-profile-button">
                        <i class="fas fa-edit"></i>
                        编辑个人资料
                    </button>
                    
                    <div class="settings-section">
                        <div class="settings-title">账户设置</div>
                        <div class="settings-item">
                            <div class="settings-item-left">
                                <div class="settings-icon">
                                    <i class="fas fa-globe"></i>
                                </div>
                                <div class="settings-label">默认货币</div>
                            </div>
                            <div style="color: var(--text-secondary);">CNY <i class="fas fa-chevron-right" style="margin-left: 8px;"></i></div>
                        </div>
                        <div class="settings-item">
                            <div class="settings-item-left">
                                <div class="settings-icon">
                                    <i class="fas fa-bell"></i>
                                </div>
                                <div class="settings-label">通知设置</div>
                            </div>
                            <i class="fas fa-chevron-right" style="color: var(--text-secondary);"></i>
                        </div>
                        <div class="settings-item">
                            <div class="settings-item-left">
                                <div class="settings-icon">
                                    <i class="fas fa-lock"></i>
                                </div>
                                <div class="settings-label">隐私与安全</div>
                            </div>
                            <i class="fas fa-chevron-right" style="color: var(--text-secondary);"></i>
                        </div>
                    </div>
                    
                    <div class="settings-section">
                        <div class="settings-title">应用设置</div>
                        <div class="settings-item">
                            <div class="settings-item-left">
                                <div class="settings-icon">
                                    <i class="fas fa-moon"></i>
                                </div>
                                <div class="settings-label">深色模式</div>
                            </div>
                            <div class="toggle off"></div>
                        </div>
                        <div class="settings-item">
                            <div class="settings-item-left">
                                <div class="settings-icon">
                                    <i class="fas fa-language"></i>
                                </div>
                                <div class="settings-label">语言</div>
                            </div>
                            <div style="color: var(--text-secondary);">简体中文 <i class="fas fa-chevron-right" style="margin-left: 8px;"></i></div>
                        </div>
                        <div class="settings-item">
                            <div class="settings-item-left">
                                <div class="settings-icon">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <div class="settings-label">关于我们</div>
                            </div>
                            <i class="fas fa-chevron-right" style="color: var(--text-secondary);"></i>
                        </div>
                    </div>
                    
                    <button class="convert-button" style="background-color: var(--error-color); margin-top: 40px;">
                        退出登录
                    </button>
                </div>
                <div class="tab-bar">
                    <div class="tab">
                        <i class="fas fa-exchange-alt"></i>
                        <span>转换</span>
                    </div>
                    <div class="tab">
                        <i class="fas fa-chart-line"></i>
                        <span>走势</span>
                    </div>
                    <div class="tab">
                        <i class="fas fa-bell"></i>
                        <span>提醒</span>
                    </div>
                    <div class="tab active">
                        <i class="fas fa-user"></i>
                        <span>我的</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 这里可以添加交互逻辑
        document.addEventListener('DOMContentLoaded', function() {
            // 示例：切换开关状态
            const toggles = document.querySelectorAll('.toggle');
            toggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    this.classList.toggle('off');
                });
            });
        });
    </script>
</body>
</html>