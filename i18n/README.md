# 国际化 (i18n) 使用指南

本项目已集成 react-i18next 来支持多语言功能。

## 支持的语言

- 中文 (zh) - 默认语言
- 英文 (en) - 备用语言

## 文件结构

```
i18n/
├── index.ts          # i18n 配置文件
├── locales/
│   ├── zh.json      # 中文翻译
│   └── en.json      # 英文翻译
└── README.md        # 使用指南
```

## 如何使用

### 1. 在组件中使用翻译

```tsx
import { useTranslation } from 'react-i18next';

function MyComponent() {
  const { t } = useTranslation();
  
  return (
    <Text>{t('app.title')}</Text>
  );
}
```

### 2. 带参数的翻译

```tsx
// 在语言文件中定义
{
  "converter": {
    "rate": "1 {{from}} = {{rate}} {{to}}"
  }
}

// 在组件中使用
<Text>{t('converter.rate', { from: 'USD', rate: 7.25, to: 'CNY' })}</Text>
```

### 3. 切换语言

```tsx
import { useTranslation } from 'react-i18next';

function LanguageSwitcher() {
  const { i18n } = useTranslation();
  
  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };
  
  return (
    <Button onPress={() => changeLanguage('en')}>English</Button>
  );
}
```

## 添加新的翻译

1. 在 `locales/zh.json` 和 `locales/en.json` 中添加相应的键值对
2. 在组件中使用 `t('your.new.key')` 来获取翻译

## 添加新语言

1. 在 `locales/` 目录下创建新的语言文件，如 `fr.json`
2. 在 `i18n/index.ts` 中导入并添加到 resources 中
3. 更新语言切换组件以支持新语言

## 注意事项

- 翻译键使用点号分隔的层级结构
- 确保所有语言文件中都有相同的键
- 使用有意义的键名，便于维护
- 对于动态内容，使用插值语法 `{{variable}}`