import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import en from './locales/en.json';
import zh from './locales/zh.json';
import { getSetting } from '@/lib/settings';

const resources = {
  en: {
    translation: en,
  },
  zh: {
    translation: zh,
  },
};

(async () => {
  const storedLang = await getSetting('language');

  i18n
    .use(initReactI18next)
    .init({
      resources,
      lng: storedLang || 'zh', // 默认语言
      fallbackLng: 'en', // 回退语言
      interpolation: {
        escapeValue: false,
      },
    });
})();

export default i18n;