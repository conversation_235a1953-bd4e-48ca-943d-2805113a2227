import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native'
import { useFonts } from 'expo-font'
import { Stack } from 'expo-router'
import 'react-native-reanimated'
import '../assets/global.css'

// import { useColorScheme } from '@/hooks/useColorScheme'
import { useAppTheme } from '@/hooks/useAppTheme'
import '@/i18n'
import { StatusBar } from 'expo-status-bar'
import { SafeAreaView } from 'react-native-safe-area-context'

export default function RootLayout() {
  const { effective } = useAppTheme()
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  })

  if (!loaded) {
    // Async font loading only occurs in development.
    return null
  }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      {/* <AuthProvider> */}
        <ThemeProvider value={effective === 'dark' ? DarkTheme : DefaultTheme}>
          <Stack screenOptions={{ headerShown: false }}>
            <Stack.Screen name="index" />
            <Stack.Screen name="+not-found" />
          </Stack>
        </ThemeProvider>
        <StatusBar style={effective === 'dark' ? 'light' : 'dark'} />
      {/* </AuthProvider> */}
    </SafeAreaView>
  )
}
