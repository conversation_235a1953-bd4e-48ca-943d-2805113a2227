import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { ActivityIndicator, Dimensions, Pressable, ScrollView, Text, View } from 'react-native'
import { useTranslation } from 'react-i18next'
import { useFocusEffect } from '@react-navigation/native'
import { LineChart } from 'react-native-chart-kit'

import { useNetworkStatus } from '@/hooks/useNetworkStatus'
import { getSetting } from '@/lib/settings'
import { fetchTimeSeriesData, AppApiError } from '@/lib/api'
import { getFavorites, toggleFavorite, isFavorite, FavoritePair } from '@/lib/favorites'

// 假设我们使用 react-native-svg-charts 或其他图表库，这里仍然保留现有可视化实现

const TIME_RANGES = [
  { key: '1W', days: 7 },
  { key: '1M', days: 30 },
  { key: '3M', days: 90 },
  { key: '6M', days: 180 },
  { key: '1Y', days: 365 },
] as const

type TimeRangeKey = typeof TIME_RANGES[number]['key']

export default function TrendScreen() {
  const { t } = useTranslation()
  const { isOffline } = useNetworkStatus()
  const [fromCurrency, setFromCurrency] = useState('USD')
  const [toCurrency, setToCurrency] = useState('CNY')
  const [timeRange, setTimeRange] = useState<TimeRangeKey>('1M')
  const [series, setSeries] = useState<{ date: string; rate: number }[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedPoint, setSelectedPoint] = useState<{ date: string; rate: number } | null>(null)
  const [favorites, setFavorites] = useState<FavoritePair[]>([])
  const [currentIsFav, setCurrentIsFav] = useState<boolean>(false)

  useFocusEffect(
    useCallback(() => {
      let active = true
      ;(async () => {
        try {
          const dc = await getSetting('defaultCurrency')
          if (active && dc) setToCurrency(dc)
          const favs = await getFavorites()
          if (active) setFavorites(favs)
        } catch {}
      })()
      return () => {
        active = false
      }
    }, [])
  )

  useEffect(() => {
    // 更新当前选中货币对的收藏状态
    (async () => {
      const fav = await isFavorite(fromCurrency, toCurrency)
      setCurrentIsFav(fav)
    })()
  }, [fromCurrency, toCurrency, favorites])

  const pairOptions = useMemo(() => {
    const bases = ['USD', 'CNY', 'EUR', 'JPY', 'GBP', 'AUD', 'CAD', 'HKD']
    return bases
      .filter((b) => b !== toCurrency)
      .map((b) => ({ base: b, quote: toCurrency }))
  }, [toCurrency])

  const selectedPair = `${fromCurrency}-${toCurrency}`

  const fetchSeries = useCallback(async () => {
    const days = TIME_RANGES.find((r) => r.key === timeRange)?.days || 30
    const end = new Date()
    const start = new Date(end)
    start.setDate(end.getDate() - days)

    try {
      setLoading(true)
      setError(null)

      if (isOffline) {
        throw new AppApiError('NETWORK_ERROR', t('common.offline'))
      }

      const data = await fetchTimeSeriesData(fromCurrency, toCurrency, start.toISOString().slice(0, 10), end.toISOString().slice(0, 10))
      const points = Object.entries(data.rates)
        .sort((a, b) => (a[0] < b[0] ? -1 : 1))
        .map(([date, rateObj]) => ({ date, rate: rateObj[toCurrency] }))
        .filter((p) => typeof p.rate === 'number' && Number.isFinite(p.rate))

      if (!points.length) {
        throw new AppApiError('INVALID_RATE', t('errors.invalidHistoricalRate'))
      }

      setSeries(points)
      setSelectedPoint(points[points.length - 1])
    } catch (e) {
      console.error('Time series fetch error:', e)

      let errorMessage = t('trends.fetchFailed')
      if (e instanceof AppApiError) {
        switch (e.code) {
          case 'NETWORK_ERROR':
            errorMessage = t('trends.networkError')
            break
          case 'HTTP_429':
            errorMessage = t('trends.rateLimited')
            break
          case 'HTTP_404':
            errorMessage = t('trends.unsupportedPair')
            break
          default:
            errorMessage = e.message || t('trends.fetchFailed')
        }
      }
      setError(errorMessage)

      // 回退：简单生成一段线性的模拟数据
      const mock: { date: string; rate: number }[] = []
      for (let i = 0; i < days; i++) {
        const d = new Date(start)
        d.setDate(start.getDate() + i)
        const base = 6 + Math.sin(i / 6) * 0.3
        const noise = (Math.random() - 0.5) * 0.1
        const rate = Math.max(0.01, base + noise)
        mock.push({ date: d.toISOString().slice(0, 10), rate: parseFloat(rate.toFixed(4)) })
      }
      setSeries(mock)
      setSelectedPoint(mock[mock.length - 1])
    } finally {
      setLoading(false)
    }
  }, [fromCurrency, toCurrency, timeRange, isOffline])

  useEffect(() => {
    fetchSeries()
  }, [fetchSeries])

  const changePair = (base: string) => {
    setFromCurrency(base)
  }

  const retry = () => fetchSeries()

  const toggleFav = async () => {
    const { favorites: updated, isNowFavorite } = await toggleFavorite(fromCurrency, toCurrency)
    setFavorites(updated)
    setCurrentIsFav(isNowFavorite)
  }

  const Chart = () => {
    const screenWidth = Dimensions.get('window').width
    const chartWidth = screenWidth - 32 // 减去 padding

    if (loading) {
      return (
        <View className="bg-white rounded-xl p-4 items-center justify-center" style={{ minHeight: 220 }}>
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text className="text-gray-500 mt-2">{t('converter.loading')}</Text>
        </View>
      )
    }

    if (!series || series.length === 0) {
      return (
        <View className="bg-white rounded-xl p-4 items-center justify-center" style={{ minHeight: 220 }}>
          <Text className="text-gray-500">{t('trends.noData')}</Text>
        </View>
      )
    }

    const chartData = {
      labels: series.map((_, index) => {
        // 只显示部分标签以避免拥挤
        if (series.length <= 7) return series[index].date.slice(5) // MM-DD
        if (index % Math.ceil(series.length / 5) === 0) return series[index].date.slice(5)
        return ''
      }),
      datasets: [
        {
          data: series.map(point => point.rate),
          color: (opacity = 1) => `rgba(59, 130, 246, ${opacity})`, // 蓝色
          strokeWidth: 2,
        },
      ],
    }

    const chartConfig = {
      backgroundColor: '#ffffff',
      backgroundGradientFrom: '#ffffff',
      backgroundGradientTo: '#ffffff',
      decimalPlaces: 4,
      color: (opacity = 1) => `rgba(59, 130, 246, ${opacity})`,
      labelColor: (opacity = 1) => `rgba(107, 114, 128, ${opacity})`,
      style: {
        borderRadius: 16,
      },
      propsForDots: {
        r: '4',
        strokeWidth: '2',
        stroke: '#3B82F6',
      },
      propsForBackgroundLines: {
        strokeDasharray: '',
        stroke: '#E5E7EB',
        strokeWidth: 1,
      },
    }

    return (
      <View className="bg-white rounded-xl p-4">
        <View className="mb-4">
          <Text className="text-lg font-semibold text-gray-800 mb-1">{selectedPair}</Text>
          <Text className="text-sm text-gray-500">
            {t('trends.selectedDataPoint', { 
              date: selectedPoint?.date || '-', 
              rate: selectedPoint?.rate?.toFixed(4) || '-' 
            })}
          </Text>
        </View>
        
        <LineChart
          data={chartData}
          width={chartWidth}
          height={200}
          chartConfig={chartConfig}
          bezier
          style={{
            marginVertical: 8,
            borderRadius: 16,
          }}
          onDataPointClick={(data) => {
            const point = series[data.index]
            if (point) {
              setSelectedPoint(point)
            }
          }}
        />
        
        <View className="flex-row justify-between mt-2">
          <Text className="text-xs text-gray-400">{timeRange}</Text>
          <Text className="text-xs text-gray-400">
            {series.length} {t('trends.dataPoints')}
          </Text>
        </View>
      </View>
    )
  }

  return (
    <ScrollView className="p-4">
      {isOffline && (
        <View className="bg-orange-100 border border-orange-300 rounded-lg p-3 mb-4">
          <Text className="text-orange-800 text-sm text-center">{t('common.offlineMode')}</Text>
        </View>
      )}

      <View className="bg-white rounded-xl p-4 mb-4">
        <View className="flex-row items-center justify-between mb-2">
          <Text className="text-lg font-bold">{t('trends.title')}</Text>
          <Pressable onPress={toggleFav} className={`px-3 py-1 rounded-full border ${currentIsFav ? 'bg-yellow-400 border-yellow-400' : 'border-gray-200'}`}>
            <Text className={`text-xs ${currentIsFav ? 'text-black' : 'text-gray-700'}`}>{currentIsFav ? `★ ${t('favorites.favorited')}` : `☆ ${t('favorites.favorite')}`}</Text>
          </Pressable>
        </View>
        <Text className="text-xs text-gray-500 mb-2">{selectedPair}</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} className="mb-2">
          <View className="flex-row gap-2">
            {pairOptions.map((p) => (
              <Pressable
                key={`${p.base}-${p.quote}`}
                onPress={() => changePair(p.base)}
                className={`px-3 py-1 rounded-full border ${p.base === fromCurrency ? 'bg-blue-600 border-blue-600' : 'border-gray-200'}`}
              >
                <Text className={`text-xs ${p.base === fromCurrency ? 'text-white' : 'text-gray-700'}`}>
                  {p.base}-{p.quote}
                </Text>
              </Pressable>
            ))}
          </View>
        </ScrollView>

        <ScrollView horizontal showsHorizontalScrollIndicator={false} className="mb-3">
          <View className="flex-row gap-2">
            {TIME_RANGES.map((r) => (
              <Pressable
                key={r.key}
                onPress={() => setTimeRange(r.key)}
                className={`px-3 py-1 rounded-full border ${r.key === timeRange ? 'bg-blue-600 border-blue-600' : 'border-gray-200'}`}
              >
                <Text className={`text-xs ${r.key === timeRange ? 'text-white' : 'text-gray-700'}`}>{r.key}</Text>
              </Pressable>
            ))}
          </View>
        </ScrollView>

        {error && (
          <View className="bg-red-50 border border-red-200 rounded-lg p-3 mb-3">
            <Text className="text-red-700 text-xs mb-2">{error}</Text>
            <Pressable onPress={retry} className="self-start px-3 py-1 rounded bg-red-600">
                <Text className="text-white text-xs">{t('converter.retry')}</Text>
              </Pressable>
          </View>
        )}

        <Chart />
      </View>

      <View className="bg-white rounded-xl p-4">
        <Text className="text-base font-semibold mb-2">{t('trends.selectedDataPoint')}</Text>
        <Text className="text-xs text-gray-500 mb-2">{t('trends.selectedDataPoint', { date: selectedPoint?.date || '-', rate: selectedPoint?.rate || '-' })}</Text>
        <Text className="text-base text-gray-700">{selectedPair}</Text>
        <Text className="text-xs text-gray-400">{timeRange}</Text>
      </View>
    </ScrollView>
  )
}
