import { IconSymbol } from '@/components/ui/IconSymbol'
import { Colors } from '@/constants/Colors'
import { dp } from '@/lib/utils'
import { Pressable, Text, View, Modal, TouchableOpacity, ScrollView, Alert } from 'react-native'
import { useEffect, useMemo, useState, useCallback } from 'react'
import { getSetting, setSetting } from '@/lib/settings'
import { useAppTheme } from '@/hooks/useAppTheme'
import { getFavorites, removeFavorite, FavoritePair } from '@/lib/favorites'
import { useFocusEffect } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'
import i18n from '@/i18n'
import { useAuth } from '@/contexts/AuthContext'
import { AuthScreen } from '@/components/auth/AuthScreen'

const currencies = ['CNY', 'USD', 'EUR', 'JPY', 'GBP']
const themeOptionsBase: Array<'system' | 'light' | 'dark'> = ['system', 'light', 'dark']

const Me = () => {
  const { user, signOut, loading } = useAuth()
  const { mode, setThemeMode, effective } = useAppTheme()
  const [defaultCurrency, setDefaultCurrency] = useState<string>('CNY')
  const [currencyModalVisible, setCurrencyModalVisible] = useState(false)
  const [themeModalVisible, setThemeModalVisible] = useState(false)
  const [favorites, setFavorites] = useState<FavoritePair[]>([])
  const { t } = useTranslation()

  // 如果用户未登录，显示登录界面
  if (!user && !loading) {
    return <AuthScreen />
  }
  const currentLangLabel = useMemo(() => (i18n.language === 'zh' ? t('settings.languageOptions.zh') : t('settings.languageOptions.en')), [i18n.language, t])
  const themeOptions = useMemo(
    () =>
      themeOptionsBase.map(v => ({
        value: v,
        label:
          v === 'system'
            ? t('settings.themeOptions.system')
            : v === 'light'
            ? t('settings.themeOptions.light')
            : t('settings.themeOptions.dark'),
      })),
    [t]
  )

  useFocusEffect(
    useCallback(() => {
      let active = true
      ;(async () => {
        try {
          const dc = await getSetting('defaultCurrency')
          if (active) setDefaultCurrency(dc)
          const favs = await getFavorites()
          if (active) setFavorites(favs)
        } catch {}
      })()
      return () => {
        active = false
      }
    }, [])
  )

  const onSelectCurrency = async (c: string) => {
    setDefaultCurrency(c)
    await setSetting('defaultCurrency', c)
    setCurrencyModalVisible(false)
  }

  const onToggleLanguage = async () => {
    const newLanguage = i18n.language === 'zh' ? 'en' : 'zh'
    await i18n.changeLanguage(newLanguage)
    await setSetting('language', newLanguage)
  }

  const onSelectTheme = async (v: 'system' | 'light' | 'dark') => {
    await setThemeMode(v)
    setThemeModalVisible(false)
  }

  const handleRemoveFavorite = (pair: FavoritePair) => {
    Alert.alert(
      t('favorites.deleteFavorite'),
      t('favorites.deleteConfirm', { pair: `${pair.base}-${pair.quote}` }),
      [
        { text: t('favorites.cancel'), style: 'cancel' },
        {
          text: t('favorites.delete'),
          style: 'destructive',
          onPress: async () => {
            const updatedFavorites = await removeFavorite(pair.id)
            setFavorites(updatedFavorites)
          },
        },
      ]
    )
  }

  return (
    <ScrollView className="flex-1 p-4 pb-20">
      <View className="flex-row items-center mb-4">
        <View className="w-10 h-10 rounded-full bg-blue-500 justify-center items-center mr-4">
          <IconSymbol name="person.fill" size={dp(20)} color="#fff" />
        </View>
        <View className="flex-1">
          <Text className="text-2xl font-bold">{user?.name || t('auth.user')}</Text>
          <Text className="text-sm text-gray-500">{user?.email || ''}</Text>
        </View>
      </View>

      <View className="rounded-xl bg-white overflow-hidden mb-4">
        <Pressable onPress={() => setCurrencyModalVisible(true)}>
          <SettingItem label={t('settings.defaultCurrency')} value={defaultCurrency} border />
        </Pressable>
        <SettingItem label={t('settings.privacyAndSecurity')} border />
        <Pressable onPress={() => setThemeModalVisible(true)}>
          <SettingItem label={t('settings.darkMode')} value={themeOptions.find(o => o.value === mode)?.label} border />
        </Pressable>
        <Pressable onPress={onToggleLanguage}>
          <SettingItem label={t('settings.language')} value={currentLangLabel} />
        </Pressable>
      </View>

      {/* 收藏管理区域 */}
      <View className="rounded-xl bg-white overflow-hidden mb-4">
        <View className="px-4 py-4 border-b border-gray-100">
          <View className="flex-row items-center justify-between">
            <Text className="text-base font-semibold">{t('favorites.title')}</Text>
            <Text className="text-xs text-gray-500">{t('favorites.count', { count: favorites.length })}</Text>
          </View>
        </View>
        
        {favorites.length === 0 ? (
          <View className="px-4 py-6">
            <Text className="text-sm text-gray-500 text-center">{t('favorites.noFavorites')}</Text>
            <Text className="text-xs text-gray-400 text-center mt-1">{t('favorites.noFavoritesHint')}</Text>
          </View>
        ) : (
          <View className="px-4 py-2">
            {favorites.map((pair, index) => (
              <View key={`${pair.base}-${pair.quote}`} className={`flex-row items-center justify-between py-3 ${index < favorites.length - 1 ? 'border-b border-gray-100' : ''}`}>
                <View className="flex-1">
                  <Text className="text-sm font-medium">{pair.base} → {pair.quote}</Text>
                  <Text className="text-xs text-gray-500">{t('favorites.addedAt')}: {new Date(pair.addedAt).toLocaleDateString()}</Text>
                </View>
                <Pressable 
                  onPress={() => handleRemoveFavorite(pair)}
                  className="px-3 py-1 bg-red-50 rounded-full"
                >
                  <Text className="text-xs text-red-600">{t('favorites.delete')}</Text>
                </Pressable>
              </View>
            ))}
          </View>
        )}
      </View>

      <Pressable 
        className="bg-red-500 rounded-xl p-4 mt-6"
        onPress={async () => {
          try {
            await signOut()
          } catch (error) {
            Alert.alert(t('auth.error'), t('auth.logoutError'))
          }
        }}
      >
        <Text className="text-sm font-bold text-center text-white">{t('settings.logout')}</Text>
      </Pressable>

      {/* 选择默认货币 */}
      <Modal transparent visible={currencyModalVisible} animationType="fade" onRequestClose={() => setCurrencyModalVisible(false)}>
        <TouchableOpacity className="flex-1 bg-black/40 justify-center" activeOpacity={1} onPress={() => setCurrencyModalVisible(false)}>
          <View className="mx-8 rounded-xl bg-white overflow-hidden">
            {currencies.map((c, idx) => (
              <Pressable key={c} onPress={() => onSelectCurrency(c)} className={`px-4 py-4 ${idx < currencies.length - 1 ? 'border-b border-gray-100' : ''}`}>
                <Text className="text-base">{c}</Text>
              </Pressable>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>

      {/* 选择主题 */}
      <Modal transparent visible={themeModalVisible} animationType="fade" onRequestClose={() => setThemeModalVisible(false)}>
        <TouchableOpacity className="flex-1 bg-black/40 justify-center" activeOpacity={1} onPress={() => setThemeModalVisible(false)}>
          <View className="mx-8 rounded-xl bg-white overflow-hidden">
            {themeOptions.map((opt, idx) => (
              <Pressable key={opt.value} onPress={() => onSelectTheme(opt.value)} className={`px-4 py-4 ${idx < themeOptions.length - 1 ? 'border-b border-gray-100' : ''}`}>
                <Text className="text-base">{opt.label}</Text>
              </Pressable>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>
    </ScrollView>
  )
}

function SettingItem({
  label,
  value,
  noChevron,
  border,
}: {
  label: string
  value?: string
  noChevron?: boolean
  border?: boolean
}) {
  return (
    <View className={`px-4 py-6 flex-row items-center justify-between ${border ? 'border-b border-gray-100' : ''}`}>
      <View className="flex-row items-center">
        <Text className="text-sm text-gray-500">{label}</Text>
      </View>
      <View className="flex-row items-center">
        {value && <Text className="text-sm text-gray-500 mr-2">{value}</Text>}
        {!noChevron && <IconSymbol name="chevron.right" size={dp(18)} color={Colors.light.text} />}
      </View>
    </View>
  )
}

export default Me
