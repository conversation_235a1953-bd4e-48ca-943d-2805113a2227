import { LinearGradient } from 'expo-linear-gradient'
import React, { useEffect, useState, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { Modal, Pressable, ScrollView, Text, TextInput, View, ActivityIndicator } from 'react-native'
import { useFocusEffect } from '@react-navigation/native'

import { LanguageSwitcher } from '@/components/LanguageSwitcher'
import { getSetting } from '@/lib/settings'
import { fetchExchangeRate, AppApiError } from '@/lib/api'
import { useNetworkStatus } from '@/hooks/useNetworkStatus'
import { getFavorites, toggleFavorite, isFavorite, FavoritePair } from '@/lib/favorites'

export default function HomeScreen() {
  const { t } = useTranslation()
  const { isOffline } = useNetworkStatus()
  const [amount, setAmount] = useState('100')
  const [fromCurrency, setFromCurrency] = useState('USD')
  const [toCurrency, setToCurrency] = useState('CNY')
  const [exchangeRate, setExchangeRate] = useState(0)
  const [lastUpdated, setLastUpdated] = useState('')
  const [isSelecting, setIsSelecting] = useState<null | 'from' | 'to'>(null)
  const [isLoadingRate, setIsLoadingRate] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [favorites, setFavorites] = useState<FavoritePair[]>([])
  const [currentIsFav, setCurrentIsFav] = useState<boolean>(false)

  // Apply default currency from settings when screen focuses
  useFocusEffect(
    useCallback(() => {
      let active = true
      ;(async () => {
        try {
          const dc = await getSetting('defaultCurrency')
          if (active && dc) setToCurrency(dc)
          const favs = await getFavorites()
          if (active) setFavorites(favs)
        } catch {}
      })()
      return () => {
        active = false
      }
    }, [])
  )

  useEffect(() => {
    // 更新当前货币对是否被收藏
    (async () => {
      const fav = await isFavorite(fromCurrency, toCurrency)
      setCurrentIsFav(fav)
    })()
  }, [fromCurrency, toCurrency, favorites])

  const currencies = [
    { code: 'USD', flag: '🇺🇸' },
    { code: 'CNY', flag: '🇨🇳' },
    { code: 'EUR', flag: '🇪🇺' },
    { code: 'JPY', flag: '🇯🇵' },
    { code: 'GBP', flag: '🇬🇧' },
    { code: 'AUD', flag: '🇦🇺' },
    { code: 'CAD', flag: '🇨🇦' },
    { code: 'HKD', flag: '🇭🇰' },
  ]

  useEffect(() => {
    getExchangeRate()
  }, [fromCurrency, toCurrency])

  const getExchangeRate = async () => {
    if (fromCurrency === toCurrency) {
      setExchangeRate(1)
      setLastUpdated(new Date().toLocaleString())
      setError(null)
      return
    }

    try {
      setIsLoadingRate(true)
      setError(null)
      
      // Check if offline
      if (isOffline) {
        throw new AppApiError('NETWORK_ERROR', t('common.offline'))
      }

      const data = await fetchExchangeRate(fromCurrency, toCurrency)
      const rate = data.rates[toCurrency]
      
      if (rate && Number.isFinite(rate)) {
        setExchangeRate(rate)
        setLastUpdated(new Date().toLocaleString())
        setError(null)
      } else {
        throw new AppApiError('INVALID_RATE', t('errors.invalidRate'))
      }
    } catch (e) {
      console.error('Exchange rate fetch error:', e)
      
      let errorMessage = t('converter.error')
      
      if (e instanceof AppApiError) {
        switch (e.code) {
          case 'NETWORK_ERROR':
            errorMessage = t('converter.error')
            break
          case 'HTTP_429':
            errorMessage = t('trends.rateLimited')
            break
          case 'HTTP_404':
            errorMessage = t('trends.unsupportedPair')
            break
          default:
            errorMessage = e.message || t('converter.error')
        }
      }
      
      setError(errorMessage)
      
      // Fallback to mock data
      const mockRates = {
        'USD-CNY': 7.25, 'USD-EUR': 0.92, 'USD-JPY': 151.67, 'USD-GBP': 0.79,
        'USD-AUD': 1.52, 'USD-CAD': 1.37, 'USD-HKD': 7.81,
        'CNY-USD': 0.14, 'CNY-EUR': 0.13, 'CNY-JPY': 20.92, 'CNY-GBP': 0.11,
        'CNY-AUD': 0.21, 'CNY-CAD': 0.19, 'CNY-HKD': 1.08,
        'EUR-USD': 1.09, 'EUR-CNY': 7.89, 'EUR-JPY': 165.02, 'EUR-GBP': 0.86,
        'EUR-AUD': 1.65, 'EUR-CAD': 1.49, 'EUR-HKD': 8.5,
        'JPY-USD': 0.0066, 'JPY-CNY': 0.048, 'JPY-EUR': 0.0061, 'JPY-GBP': 0.0052,
        'JPY-AUD': 0.01, 'JPY-CAD': 0.009, 'JPY-HKD': 0.051,
        'GBP-USD': 1.27, 'GBP-CNY': 9.2, 'GBP-EUR': 1.17, 'GBP-JPY': 192.58,
        'GBP-AUD': 1.93, 'GBP-CAD': 1.74, 'GBP-HKD': 9.91,
        'AUD-USD': 0.66, 'AUD-CNY': 4.77, 'AUD-EUR': 0.61, 'AUD-JPY': 99.78,
        'AUD-GBP': 0.52, 'AUD-CAD': 0.9, 'AUD-HKD': 5.14,
        'CAD-USD': 0.73, 'CAD-CNY': 5.3, 'CAD-EUR': 0.67, 'CAD-JPY': 110.87,
        'CAD-GBP': 0.58, 'CAD-AUD': 1.11, 'CAD-HKD': 5.71,
        'HKD-USD': 0.13, 'HKD-CNY': 0.93, 'HKD-EUR': 0.12, 'HKD-JPY': 19.42,
        'HKD-GBP': 0.1, 'HKD-AUD': 0.19, 'HKD-CAD': 0.18,
      } as const

      const key = `${fromCurrency}-${toCurrency}` as keyof typeof mockRates
      const rate = mockRates[key]
      setExchangeRate(rate || 1)
      setLastUpdated(new Date().toLocaleString())
    } finally {
      setIsLoadingRate(false)
    }
  }

  const swapCurrencies = () => {
    setFromCurrency(toCurrency)
    setToCurrency(fromCurrency)
  }

  const getConvertedAmount = () => {
    const numericAmount = parseFloat(amount) || 0
    return (numericAmount * exchangeRate).toFixed(2)
  }

  const getCurrencyInfo = (code: string) => {
    return currencies.find((c) => c.code === code) || { code, flag: '🏳️' }
  }

  const openPicker = (side: 'from' | 'to') => setIsSelecting(side)
  const closePicker = () => setIsSelecting(null)
  const handlePick = (code: string) => {
    if (isSelecting === 'from') setFromCurrency(code)
    if (isSelecting === 'to') setToCurrency(code)
    closePicker()
  }

  const retryFetch = () => {
    getExchangeRate()
  }

  const toggleFav = async () => {
    const { favorites: updated, isNowFavorite } = await toggleFavorite(fromCurrency, toCurrency)
    setFavorites(updated)
    setCurrentIsFav(isNowFavorite)
  }

  const applyFavorite = (pair: FavoritePair) => {
    setFromCurrency(pair.base)
    setToCurrency(pair.quote)
  }

  return (
    <ScrollView className="p-4">
      {/* Network status indicator */}
      {isOffline && (
        <View className="bg-orange-100 border border-orange-300 rounded-lg p-3 mb-4">
          <Text className="text-orange-800 text-sm text-center">
            {t('common.offlineMode')}
          </Text>
        </View>
      )}

      <View className="rounded-lg bg-white p-4 mb-4">
        <View className="flex-row items-center justify-between mb-4">
          <Text className="text-2xl font-bold">{t('app.title')}</Text>
          <View className="flex-row items-center gap-2">
            <Pressable onPress={toggleFav} className={`px-3 py-1 rounded-full border mr-2 ${currentIsFav ? 'bg-yellow-400 border-yellow-400' : 'border-gray-200'}`}>
              <Text className={`text-xs ${currentIsFav ? 'text-black' : 'text-gray-700'}`}>{currentIsFav ? `★ ${t('favorites.favorited')}` : `☆ ${t('favorites.favorite')}`}</Text>
            </Pressable>
            <LanguageSwitcher />
          </View>
        </View>
        <View>
          <Text className="text-md mb-2">{t('converter.amount')}</Text>
          <TextInput
            className="border border-gray-200 rounded-md p-2"
            value={amount}
            onChangeText={setAmount}
            keyboardType="numeric"
            placeholder={t('converter.amountPlaceholder')}
          />
        </View>

        <View className="my-4 flex-row items-center justify-between">
          <Pressable onPress={() => openPicker('from')} className="border border-gray-200 rounded-md p-2 flex-1 items-center justify-center">
            <Text className="text-sm text-center font-bold text-gray-500">
              {getCurrencyInfo(fromCurrency).flag} {fromCurrency}
            </Text>
          </Pressable>

          <Pressable onPress={swapCurrencies} className="mx-2">
            <Text className="text-sm text-center font-bold">⇄</Text>
          </Pressable>

          <Pressable onPress={() => openPicker('to')} className="border border-gray-200 rounded-md p-2 flex-1 items-center justify-center">
            <Text className="text-sm text-center font-bold text-gray-500">
              {getCurrencyInfo(toCurrency).flag} {toCurrency}
            </Text>
          </Pressable>
        </View>
        
        <View className="relative items-center justify-center overflow-hidden rounded-xl py-4 gap-2">
          <LinearGradient
            colors={['#4299e1', '#60a5fa']}
            className="absolute top-0 left-0 right-0 bottom-0 bg-gradient-to-r from-blue-500 to-blue-600"
          />
          <Text className="text-white text-sm">{t('converter.result')}</Text>
          <Text className="text-white text-3xl font-bold">
            {getConvertedAmount()} {toCurrency}
          </Text>
          <View className="flex-row items-center">
            <Text className="text-white text-sm">
              {t('converter.rate', { from: fromCurrency, rate: isLoadingRate ? '...' : exchangeRate, to: toCurrency })}
            </Text>
            {isLoadingRate && (
              <ActivityIndicator size="small" color="white" style={{ marginLeft: 8 }} />
            )}
          </View>
          {error && (
            <View className="flex-row items-center mt-1">
              <Text className="text-xs text-red-100">{error}</Text>
              {!isLoadingRate && (
                <Pressable onPress={retryFetch} className="ml-2 bg-white/20 px-2 py-1 rounded">
                  <Text className="text-xs text-white">{t('converter.retry')}</Text>
                </Pressable>
              )}
            </View>
          )}
        </View>
        
        <View className="flex-row mt-2 py-2 items-end justify-end">
          <Text className="text-xs text-gray-500">{t('converter.lastUpdated', { time: lastUpdated })}</Text>
        </View>

        {/* 收藏列表 */}
        <View className="mt-2">
          <View className="flex-row items-center justify-between mb-2">
            <Text className="text-sm font-semibold">{t('favorites.favoritesList')}</Text>
            <Text className="text-xs text-gray-500">{t('favorites.count', { count: favorites.length })}</Text>
          </View>
          {favorites.length === 0 ? (
            <View className="bg-gray-50 border border-dashed border-gray-200 rounded-lg p-3">
              <Text className="text-xs text-gray-500">{t('favorites.noFavorites')}</Text>
            </View>
          ) : (
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View className="flex-row gap-2">
                {favorites.map((p) => (
                  <Pressable
                    key={`${p.base}-${p.quote}`}
                    onPress={() => applyFavorite(p)}
                    className={`px-3 py-1 rounded-full border ${p.base === fromCurrency && p.quote === toCurrency ? 'bg-blue-600 border-blue-600' : 'border-gray-200'}`}
                  >
                    <Text className={`text-xs ${p.base === fromCurrency && p.quote === toCurrency ? 'text-white' : 'text-gray-700'}`}>
                      {p.base}-{p.quote}
                    </Text>
                  </Pressable>
                ))}
              </View>
            </ScrollView>
          )}
        </View>
      </View>

      <View className="bg-white rounded-xl p-4">
        <Text className="text-lg font-bold mb-4">Popular Exchange Rates</Text>
        <View className="flex-row w-full flex-wrap gap-3">
          {currencies.slice(1).map((currency) => {
            // 简化示例：相对当前汇率的倒数作为展示
            const rate = (1 / (exchangeRate || 1)).toFixed(4)
            const change = (Math.random() * 2).toFixed(2)
            const isPositive = Math.random() > 0.5

            return (
              <View key={currency.code} className="bg-gray-50 rounded-xl p-3 shadow-sm" style={{ width: '48%' }}>
                <View className="flex-row justify-between items-start">
                  <View className="flex-1">
                    <View className="flex-row items-center mb-1">
                      <Text className="text-sm mr-2">{currency.flag}</Text>
                      <Text className="text-sm font-bold text-gray-800">{currency.code}/USD</Text>
                    </View>
                    <Text className="text-xs text-gray-600">{t(`currencies.${currency.code}`)}</Text>
                  </View>
                  <View className="items-end">
                    <Text className="text-sm font-bold text-gray-800">{rate}</Text>
                    <Text className={`text-xs ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
                      {isPositive ? '+' : '-'}
                      {change}%
                    </Text>
                  </View>
                </View>
              </View>
            )
          })}
        </View>
      </View>

      {/* 货币选择器 */}
      <Modal transparent visible={!!isSelecting} animationType="fade" onRequestClose={closePicker}>
        <Pressable className="flex-1 bg-black/30" onPress={closePicker}>
          <View className="mt-auto bg-white rounded-t-2xl p-4">
            <Text className="text-center text-base font-bold mb-3">
              {isSelecting === 'from' ? t('converter.from') : t('converter.to')}
            </Text>
            <View className="max-h-96">
              <ScrollView>
                {currencies.map((c) => (
                  <Pressable
                    key={c.code}
                    className="flex-row justify-between items-center px-2 py-3 border-b border-gray-100"
                    onPress={() => handlePick(c.code)}
                  >
                    <View className="flex-row items-center">
                      <Text className="text-base mr-2">{c.flag}</Text>
                      <Text className="text-base font-semibold">{c.code}</Text>
                    </View>
                    {(isSelecting === 'from' ? fromCurrency : toCurrency) === c.code && (
                      <Text className="text-blue-500">✓</Text>
                    )}
                  </Pressable>
                ))}
              </ScrollView>
            </View>
          </View>
        </Pressable>
      </Modal>
    </ScrollView>
  )
}
